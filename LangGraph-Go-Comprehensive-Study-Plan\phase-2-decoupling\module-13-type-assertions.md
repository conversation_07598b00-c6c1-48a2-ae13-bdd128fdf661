# Module 13: Type Assertions & Interface Design

## Video Coverage

**5.4-5.6 Conversion & Assertions (9:02), Interface Pollution (6:45), Mocking (5:53)**

## Learning Objectives

- Master type assertions and conversions
- Avoid interface pollution and over-abstraction
- Implement robust type system for LangGraph

## Hands-on Tasks

1. **Implement type assertion examples from video transcripts**
   - Study type assertion syntax and safety patterns
   - Practice interface{} to concrete type assertions
   - Understand the comma ok idiom for safe assertions
   - Implement examples showing assertion failures and recovery

2. **Add safe type assertions for LangGraph node types**
   - Create type-safe node type checking:

     ```go
     func ProcessNode(node NodeInterface) error {
         switch n := node.(type) {
         case *ProcessingNode:
             return n.ValidateFunction()
         case *ConditionalNode:
             return n.ValidateCondition()
         case *InputNode:
             return n.ValidateInput()
         default:
             return fmt.Errorf("unknown node type: %T", node)
         }
     }
     
     func GetProcessingNode(node NodeInterface) (*ProcessingNode, error) {
         if pn, ok := node.(*ProcessingNode); ok {
             return pn, nil
         }
         return nil, fmt.Errorf("node %s is not a ProcessingNode, got %T", node.ID(), node)
     }
     
     func IsExecutableNode(node NodeInterface) bool {
         _, ok := node.(interface {
             Execute(ctx context.Context, input interface{}) (interface{}, error)
         })
         return ok
     }
     ```

3. **Refactor interfaces to avoid pollution**
   - Identify and fix over-abstracted interfaces:

     ```go
     // BAD: Interface pollution - too many methods
     type BadNodeInterface interface {
         ID() string
         Type() NodeType
         Execute(ctx context.Context, input interface{}) (interface{}, error)
         Validate() error
         Serialize() ([]byte, error)
         Deserialize([]byte) error
         Clone() NodeInterface
         Hash() string
         Compare(NodeInterface) bool
         GetMetadata() map[string]interface{}
         SetMetadata(string, interface{})
         GetDependencies() []string
         SetDependencies([]string)
     }
     
     // GOOD: Focused interfaces
     type Node interface {
         ID() string
         Type() NodeType
     }
     
     type Executable interface {
         Execute(ctx context.Context, input interface{}) (interface{}, error)
     }
     
     type Validator interface {
         Validate() error
     }
     
     type Serializable interface {
         Serialize() ([]byte, error)
         Deserialize([]byte) error
     }
     
     type MetadataProvider interface {
         GetMetadata() map[string]interface{}
         SetMetadata(string, interface{})
     }
     ```

4. **Create mockable interfaces for testing**
   - Design interfaces that are easy to mock:

     ```go
     type StateStore interface {
         Get(key string) (interface{}, error)
         Set(key string, value interface{}) error
         Delete(key string) error
     }
     
     // Mock implementation for testing
     type MockStateStore struct {
         data   map[string]interface{}
         errors map[string]error // Simulate errors for specific keys
     }
     
     func NewMockStateStore() *MockStateStore {
         return &MockStateStore{
             data:   make(map[string]interface{}),
             errors: make(map[string]error),
         }
     }
     
     func (m *MockStateStore) Get(key string) (interface{}, error) {
         if err, exists := m.errors[key]; exists {
             return nil, err
         }
         value, exists := m.data[key]
         if !exists {
             return nil, fmt.Errorf("key %s not found", key)
         }
         return value, nil
     }
     
     func (m *MockStateStore) Set(key string, value interface{}) error {
         if err, exists := m.errors[key]; exists {
             return err
         }
         m.data[key] = value
         return nil
     }
     
     func (m *MockStateStore) Delete(key string) error {
         if err, exists := m.errors[key]; exists {
             return err
         }
         delete(m.data, key)
         return nil
     }
     
     func (m *MockStateStore) SetError(key string, err error) {
         m.errors[key] = err
     }
     ```

5. **Implement type-safe conversion utilities**
   - Create utilities for safe type conversions:

     ```go
     func ConvertToString(value interface{}) (string, error) {
         switch v := value.(type) {
         case string:
             return v, nil
         case []byte:
             return string(v), nil
         case fmt.Stringer:
             return v.String(), nil
         case int, int8, int16, int32, int64:
             return fmt.Sprintf("%d", v), nil
         case float32, float64:
             return fmt.Sprintf("%f", v), nil
         case bool:
             return fmt.Sprintf("%t", v), nil
         default:
             return "", fmt.Errorf("cannot convert %T to string", value)
         }
     }
     
     func ConvertToInt(value interface{}) (int, error) {
         switch v := value.(type) {
         case int:
             return v, nil
         case int8:
             return int(v), nil
         case int16:
             return int(v), nil
         case int32:
             return int(v), nil
         case int64:
             if v > math.MaxInt || v < math.MinInt {
                 return 0, fmt.Errorf("int64 value %d overflows int", v)
             }
             return int(v), nil
         case float32:
             return int(v), nil
         case float64:
             return int(v), nil
         case string:
             return strconv.Atoi(v)
         default:
             return 0, fmt.Errorf("cannot convert %T to int", value)
         }
     }
     ```

## Key Concepts

- **Type assertions**: Converting interface{} to concrete types
- **Interface design**: Creating focused, minimal interfaces
- **Mocking**: Designing testable interfaces
- **Type safety**: Safe type conversions and assertions

## Code Examples

### Safe Type Assertions

```go
func ProcessValue(value interface{}) error {
    // Type switch - preferred for multiple types
    switch v := value.(type) {
    case string:
        fmt.Printf("Processing string: %s\n", v)
    case int:
        fmt.Printf("Processing int: %d\n", v)
    case []byte:
        fmt.Printf("Processing bytes: %s\n", string(v))
    default:
        return fmt.Errorf("unsupported type: %T", value)
    }
    return nil
}

func GetStringValue(value interface{}) (string, bool) {
    // Comma ok idiom - preferred for single type
    str, ok := value.(string)
    return str, ok
}

func MustGetString(value interface{}) string {
    // Assertion without check - use carefully
    return value.(string) // Panics if value is not string
}
```

### Interface Design Patterns

```go
// Small, focused interfaces
type Reader interface {
    Read([]byte) (int, error)
}

type Writer interface {
    Write([]byte) (int, error)
}

type Closer interface {
    Close() error
}

// Compose when needed
type ReadWriteCloser interface {
    Reader
    Writer
    Closer
}

// Optional behavior through interface checking
func OptionalClose(obj interface{}) {
    if closer, ok := obj.(Closer); ok {
        closer.Close()
    }
}
```

### Mock Implementation Pattern

```go
type HTTPClient interface {
    Get(url string) (*http.Response, error)
    Post(url string, body io.Reader) (*http.Response, error)
}

type MockHTTPClient struct {
    responses map[string]*http.Response
    errors    map[string]error
    calls     []string
}

func NewMockHTTPClient() *MockHTTPClient {
    return &MockHTTPClient{
        responses: make(map[string]*http.Response),
        errors:    make(map[string]error),
        calls:     make([]string, 0),
    }
}

func (m *MockHTTPClient) Get(url string) (*http.Response, error) {
    m.calls = append(m.calls, "GET "+url)
    
    if err, exists := m.errors[url]; exists {
        return nil, err
    }
    
    if resp, exists := m.responses[url]; exists {
        return resp, nil
    }
    
    return &http.Response{StatusCode: 200}, nil
}

func (m *MockHTTPClient) Post(url string, body io.Reader) (*http.Response, error) {
    m.calls = append(m.calls, "POST "+url)
    return m.Get(url) // Reuse Get logic for simplicity
}

func (m *MockHTTPClient) SetResponse(url string, resp *http.Response) {
    m.responses[url] = resp
}

func (m *MockHTTPClient) SetError(url string, err error) {
    m.errors[url] = err
}

func (m *MockHTTPClient) GetCalls() []string {
    return m.calls
}
```

### Type-Safe Conversion Utilities

```go
type Converter struct{}

func (c *Converter) ToStringSlice(value interface{}) ([]string, error) {
    switch v := value.(type) {
    case []string:
        return v, nil
    case []interface{}:
        result := make([]string, len(v))
        for i, item := range v {
            str, err := ConvertToString(item)
            if err != nil {
                return nil, fmt.Errorf("failed to convert item %d: %w", i, err)
            }
            result[i] = str
        }
        return result, nil
    default:
        return nil, fmt.Errorf("cannot convert %T to []string", value)
    }
}

func (c *Converter) ToMap(value interface{}) (map[string]interface{}, error) {
    switch v := value.(type) {
    case map[string]interface{}:
        return v, nil
    case map[interface{}]interface{}:
        result := make(map[string]interface{})
        for key, val := range v {
            strKey, err := ConvertToString(key)
            if err != nil {
                return nil, fmt.Errorf("failed to convert key %v: %w", key, err)
            }
            result[strKey] = val
        }
        return result, nil
    default:
        return nil, fmt.Errorf("cannot convert %T to map[string]interface{}", value)
    }
}
```

## Resources

- Ultimate Go Programming 5.4-5.6 transcripts
- [Type Assertions](https://tour.golang.org/methods/15)
- [Interface Best Practices](https://golang.org/doc/effective_go#interfaces)
- [Go Proverbs](https://go-proverbs.github.io/) - "The bigger the interface, the weaker the abstraction"

## Validation Checklist

- [ ] Type assertion examples from transcripts implemented
- [ ] Safe type assertions added for LangGraph node types
- [ ] Interfaces refactored to avoid pollution
- [ ] Mockable interfaces created for testing
- [ ] Type-safe conversion utilities implemented
- [ ] Interface design follows Go best practices
