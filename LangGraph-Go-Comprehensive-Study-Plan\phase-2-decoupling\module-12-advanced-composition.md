# Module 12: Advanced Composition Patterns

## Video Coverage

**5.1-5.3 Grouping Types (12:38), Decoupling Parts 1-3 (6:58, 18:25, 14:36)**

## Learning Objectives

- Master advanced composition and decoupling strategies
- Understand grouping types and interface design
- Implement flexible LangGraph architecture

## Hands-on Tasks

1. **Implement composition examples from video transcripts**
   - Study grouping types patterns from the video
   - Practice decoupling strategies and interface design
   - Understand when to use composition vs embedding
   - Implement examples showing different composition approaches

2. **Group related LangGraph types into cohesive packages**
   - Organize types by functionality and responsibility:

     ```go
     // pkg/execution/
     type Executor interface {
         Execute(ctx context.Context, graph *Graph) (*Result, error)
     }
     
     type SequentialExecutor struct {
         timeout time.Duration
         logger  Logger
     }
     
     type ParallelExecutor struct {
         maxWorkers int
         timeout    time.Duration
         logger     Logger
     }
     
     // pkg/storage/
     type StateStore interface {
         Save(key string, state interface{}) error
         Load(key string) (interface{}, error)
         Delete(key string) error
     }
     
     type MemoryStore struct {
         data map[string]interface{}
         mu   sync.RWMutex
     }
     
     type FileStore struct {
         basePath string
         encoder  Encoder
     }
     ```

3. **Create decoupled interfaces for different concerns**
   - Separate interfaces by responsibility:

     ```go
     // Execution concern
     type NodeExecutor interface {
         ExecuteNode(ctx context.Context, node Node, input interface{}) (interface{}, error)
     }
     
     // State management concern
     type StateManager interface {
         GetState(key string) (interface{}, bool)
         SetState(key string, value interface{}) error
         ClearState() error
     }
     
     // Communication concern
     type MessageBroker interface {
         Publish(topic string, message interface{}) error
         Subscribe(topic string, handler func(interface{})) error
         Unsubscribe(topic string) error
     }
     
     // Monitoring concern
     type MetricsCollector interface {
         RecordExecution(nodeID string, duration time.Duration)
         RecordError(nodeID string, err error)
         GetMetrics() map[string]interface{}
     }
     ```

4. **Implement strategy patterns for execution algorithms**
   - Create pluggable execution strategies:

     ```go
     type ExecutionStrategy interface {
         Execute(ctx context.Context, nodes []Node, edges []Edge) (*ExecutionResult, error)
         Name() string
     }
     
     type SequentialStrategy struct {
         timeout time.Duration
     }
     
     func (s *SequentialStrategy) Execute(ctx context.Context, nodes []Node, edges []Edge) (*ExecutionResult, error) {
         result := &ExecutionResult{
             StartTime: time.Now(),
             Strategy:  s.Name(),
         }
         
         for _, node := range nodes {
             output, err := node.Execute(ctx, nil)
             if err != nil {
                 result.Error = err
                 return result, err
             }
             result.Outputs[node.ID()] = output
         }
         
         result.EndTime = time.Now()
         return result, nil
     }
     
     func (s *SequentialStrategy) Name() string {
         return "sequential"
     }
     
     type ParallelStrategy struct {
         maxConcurrency int
         timeout        time.Duration
     }
     
     func (p *ParallelStrategy) Execute(ctx context.Context, nodes []Node, edges []Edge) (*ExecutionResult, error) {
         // Parallel execution implementation
         return nil, nil
     }
     
     func (p *ParallelStrategy) Name() string {
         return "parallel"
     }
     ```

5. **Add flexible configuration and extension points**
   - Create extensible configuration system:

     ```go
     type Config interface {
         Get(key string) interface{}
         Set(key string, value interface{}) error
         Validate() error
     }
     
     type ExecutionConfig struct {
         Strategy       string                 `json:"strategy"`
         Timeout        time.Duration         `json:"timeout"`
         MaxRetries     int                   `json:"max_retries"`
         Extensions     map[string]interface{} `json:"extensions"`
         Middleware     []string              `json:"middleware"`
     }
     
     type ConfigurableExecutor struct {
         config     Config
         strategies map[string]ExecutionStrategy
         middleware []Middleware
     }
     
     func (ce *ConfigurableExecutor) AddStrategy(name string, strategy ExecutionStrategy) {
         ce.strategies[name] = strategy
     }
     
     func (ce *ConfigurableExecutor) AddMiddleware(mw Middleware) {
         ce.middleware = append(ce.middleware, mw)
     }
     ```

## Key Concepts

- **Composition**: Building complex behavior from simple components
- **Decoupling**: Separating concerns through interfaces
- **Grouping**: Organizing related types and functionality
- **Strategy patterns**: Pluggable algorithms and behaviors

## Code Examples

### Grouping Related Types

```go
// pkg/graph/types.go - Core graph types
package graph

type Node interface {
    ID() string
    Execute(ctx context.Context, input interface{}) (interface{}, error)
}

type Edge struct {
    From, To string
    Weight   float64
}

type Graph struct {
    nodes map[string]Node
    edges []Edge
}

// pkg/graph/builder.go - Graph construction
type Builder struct {
    graph *Graph
}

func NewBuilder() *Builder {
    return &Builder{
        graph: &Graph{
            nodes: make(map[string]Node),
            edges: make([]Edge, 0),
        },
    }
}

// pkg/execution/engine.go - Execution logic
package execution

import "your-project/pkg/graph"

type Engine struct {
    strategy Strategy
    config   Config
}

func (e *Engine) Execute(g *graph.Graph) (*Result, error) {
    return e.strategy.Execute(g)
}
```

### Decoupled Interface Design

```go
// Separate concerns with focused interfaces
type Reader interface {
    Read(key string) ([]byte, error)
}

type Writer interface {
    Write(key string, data []byte) error
}

type Deleter interface {
    Delete(key string) error
}

// Compose interfaces as needed
type ReadWriter interface {
    Reader
    Writer
}

type Storage interface {
    Reader
    Writer
    Deleter
}

// Implementations can satisfy specific interfaces
type ReadOnlyCache struct {
    data map[string][]byte
}

func (roc *ReadOnlyCache) Read(key string) ([]byte, error) {
    data, exists := roc.data[key]
    if !exists {
        return nil, fmt.Errorf("key %s not found", key)
    }
    return data, nil
}

// ReadOnlyCache only satisfies Reader interface
var _ Reader = (*ReadOnlyCache)(nil)
```

### Strategy Pattern Implementation

```go
type SortStrategy interface {
    Sort(data []int) []int
    Name() string
}

type BubbleSort struct{}

func (bs *BubbleSort) Sort(data []int) []int {
    result := make([]int, len(data))
    copy(result, data)
    
    n := len(result)
    for i := 0; i < n-1; i++ {
        for j := 0; j < n-i-1; j++ {
            if result[j] > result[j+1] {
                result[j], result[j+1] = result[j+1], result[j]
            }
        }
    }
    return result
}

func (bs *BubbleSort) Name() string { return "bubble" }

type QuickSort struct{}

func (qs *QuickSort) Sort(data []int) []int {
    result := make([]int, len(data))
    copy(result, data)
    qs.quickSort(result, 0, len(result)-1)
    return result
}

func (qs *QuickSort) quickSort(arr []int, low, high int) {
    if low < high {
        pi := qs.partition(arr, low, high)
        qs.quickSort(arr, low, pi-1)
        qs.quickSort(arr, pi+1, high)
    }
}

func (qs *QuickSort) partition(arr []int, low, high int) int {
    pivot := arr[high]
    i := low - 1
    
    for j := low; j < high; j++ {
        if arr[j] < pivot {
            i++
            arr[i], arr[j] = arr[j], arr[i]
        }
    }
    arr[i+1], arr[high] = arr[high], arr[i+1]
    return i + 1
}

func (qs *QuickSort) Name() string { return "quick" }

// Context that uses strategies
type Sorter struct {
    strategy SortStrategy
}

func (s *Sorter) SetStrategy(strategy SortStrategy) {
    s.strategy = strategy
}

func (s *Sorter) Sort(data []int) []int {
    if s.strategy == nil {
        s.strategy = &QuickSort{} // default strategy
    }
    return s.strategy.Sort(data)
}
```

## Resources

- Ultimate Go Programming 5.1-5.3 transcripts
- [Go Package Design](https://golang.org/doc/code.html#Organization)
- [Interface Segregation Principle](https://en.wikipedia.org/wiki/Interface_segregation_principle)
- [Strategy Pattern in Go](https://refactoring.guru/design-patterns/strategy/go/example)

## Validation Checklist

- [ ] Composition examples from transcripts implemented
- [ ] Related LangGraph types grouped into cohesive packages
- [ ] Decoupled interfaces created for different concerns
- [ ] Strategy patterns implemented for execution algorithms
- [ ] Flexible configuration and extension points added
- [ ] Package organization follows Go conventions
