# Module 9: Methods & Receiver Semantics

## Video Coverage

**4.1 Methods Parts 1-3 (10:45, 15:35, 13:40)**

## Learning Objectives

- Master method declaration and receiver behavior
- Understand value vs pointer semantics for methods
- Implement core LangGraph behaviors with proper semantics

## Hands-on Tasks

1. **Implement method examples from video transcripts**
   - Study method declaration syntax from video examples
   - Practice both value and pointer receiver patterns
   - Understand method promotion and embedding
   - Implement examples showing receiver behavior differences

2. **Add methods to Node, Edge, and Graph types**
   - Implement core methods for Node type:

     ```go
     // Value receiver for read-only operations
     func (n Node) ID() string {
         return n.id
     }
     
     func (n Node) Type() NodeType {
         return n.nodeType
     }
     
     // Pointer receiver for mutations
     func (n *Node) SetState(state ExecutionState) {
         n.state = state
     }
     
     func (n *Node) Execute(input interface{}) (interface{}, error) {
         if n.function == nil {
             return nil, fmt.Errorf("node %s has no execution function", n.id)
         }
         return n.function(input)
     }
     ```

3. **Choose appropriate receiver semantics for each method**
   - Create guidelines for receiver choice:
     - Use value receivers for small structs and read-only operations
     - Use pointer receivers for mutations and large structs
     - Be consistent within a type's method set
   - Document receiver decisions for each method
   - Implement both patterns and explain the reasoning

4. **Implement method chaining patterns for LangGraph operations**
   - Create fluent interfaces for graph building:

     ```go
     func (g *Graph) AddNode(node *Node) *Graph {
         g.nodes[node.ID()] = node
         return g
     }
     
     func (g *Graph) AddEdge(from, to string) *Graph {
         edge := Edge{From: from, To: to}
         g.edges = append(g.edges, edge)
         return g
     }
     
     func (g *Graph) SetEntryPoint(nodeID string) *Graph {
         g.entryPoint = nodeID
         return g
     }
     
     // Usage: graph.AddNode(node1).AddNode(node2).AddEdge("node1", "node2")
     ```

5. **Add method documentation following Go conventions**
   - Write comprehensive method documentation
   - Include examples in documentation comments
   - Document receiver choice rationale
   - Add usage examples for complex methods

## Key Concepts

- **Methods**: Functions with a special receiver argument
- **Receivers**: The type that a method is defined on
- **Value/pointer semantics**: Choosing appropriate receiver types
- **API design**: Creating intuitive and consistent method interfaces

## Code Examples

### Method Declaration Patterns

```go
type Counter struct {
    value int
    name  string
}

// Value receiver - good for small types and read-only operations
func (c Counter) Value() int {
    return c.value
}

func (c Counter) Name() string {
    return c.name
}

// Pointer receiver - required for mutations
func (c *Counter) Increment() {
    c.value++
}

func (c *Counter) Reset() {
    c.value = 0
}

// Pointer receiver - good for large types to avoid copying
func (c *Counter) String() string {
    return fmt.Sprintf("%s: %d", c.name, c.value)
}
```

### Method Sets and Interface Satisfaction

```go
type Executable interface {
    Execute(input interface{}) (interface{}, error)
}

type Resettable interface {
    Reset()
}

// Node with pointer receiver methods
type Node struct {
    id       string
    function func(interface{}) (interface{}, error)
}

func (n *Node) Execute(input interface{}) (interface{}, error) {
    return n.function(input)
}

func (n *Node) Reset() {
    n.function = nil
}

// Only *Node satisfies both interfaces due to pointer receiver methods
var _ Executable = (*Node)(nil)  // ✓ Valid
var _ Resettable = (*Node)(nil)  // ✓ Valid
// var _ Executable = Node{}     // ✗ Invalid - value doesn't have pointer methods
```

### Fluent Interface Pattern

```go
type GraphBuilder struct {
    graph *Graph
}

func NewGraphBuilder() *GraphBuilder {
    return &GraphBuilder{
        graph: &Graph{
            nodes: make(map[string]*Node),
            edges: make([]Edge, 0),
        },
    }
}

func (gb *GraphBuilder) WithNode(id string, fn func(interface{}) (interface{}, error)) *GraphBuilder {
    node := &Node{
        id:       id,
        function: fn,
    }
    gb.graph.nodes[id] = node
    return gb
}

func (gb *GraphBuilder) WithEdge(from, to string) *GraphBuilder {
    edge := Edge{From: from, To: to}
    gb.graph.edges = append(gb.graph.edges, edge)
    return gb
}

func (gb *GraphBuilder) Build() *Graph {
    return gb.graph
}

// Usage
graph := NewGraphBuilder().
    WithNode("input", inputProcessor).
    WithNode("process", dataProcessor).
    WithEdge("input", "process").
    Build()
```

## Resources

- Ultimate Go Programming 4.1 Methods Parts 1-3 transcripts
- [Go Methods](https://tour.golang.org/methods/1)
- [Method Sets](https://golang.org/ref/spec#Method_sets)
- [Effective Go - Methods](https://golang.org/doc/effective_go#methods)

## Validation Checklist

- [ ] Method examples from transcripts implemented and understood
- [ ] Core methods added to Node, Edge, and Graph types
- [ ] Receiver semantics documented and consistently applied
- [ ] Method chaining patterns implemented for graph operations
- [ ] Comprehensive method documentation written
- [ ] Method sets and interface satisfaction understood
