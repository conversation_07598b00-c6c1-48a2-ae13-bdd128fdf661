# Module 25: Channels & Communication Patterns

## Video Coverage

**10.1-10.6 Signaling Semantics (17:50), Basic Patterns Parts 1-3 (21:30 total), Pooling Pattern (6:23)**

## Learning Objectives

- Master channel mechanics and communication patterns
- Understand signaling semantics and synchronization
- Implement LangGraph's channel-based communication system

## Hands-on Tasks

1. **Implement channel examples from video transcripts**
   - Study channel creation, sending, and receiving
   - Practice buffered vs unbuffered channels
   - Understand channel closing and range operations
   - Implement signaling patterns with channels

2. **Create channel-based message passing for LangGraph nodes**
   - Design node communication system:

     ```go
     type NodeMessage struct {
         FromNodeID string
         ToNodeID   string
         Data       interface{}
         Timestamp  time.Time
         MessageID  string
     }
     
     type ChannelManager struct {
         channels map[string]chan NodeMessage
         mu       sync.RWMutex
         closed   bool
     }
     
     func NewChannelManager() *ChannelManager {
         return &ChannelManager{
             channels: make(map[string]chan NodeMessage),
         }
     }
     
     func (cm *ChannelManager) CreateChannel(nodeID string, bufferSize int) {
         cm.mu.Lock()
         defer cm.mu.Unlock()
         
         if cm.closed {
             return
         }
         
         cm.channels[nodeID] = make(chan NodeMessage, bufferSize)
     }
     
     func (cm *ChannelManager) SendMessage(toNodeID string, message NodeMessage) error {
         cm.mu.RLock()
         defer cm.mu.RUnlock()
         
         if cm.closed {
             return fmt.Errorf("channel manager is closed")
         }
         
         ch, exists := cm.channels[toNodeID]
         if !exists {
             return fmt.Errorf("channel for node %s does not exist", toNodeID)
         }
         
         select {
         case ch <- message:
             return nil
         default:
             return fmt.Errorf("channel for node %s is full", toNodeID)
         }
     }
     
     func (cm *ChannelManager) ReceiveMessage(nodeID string) (NodeMessage, error) {
         cm.mu.RLock()
         defer cm.mu.RUnlock()
         
         ch, exists := cm.channels[nodeID]
         if !exists {
             return NodeMessage{}, fmt.Errorf("channel for node %s does not exist", nodeID)
         }
         
         select {
         case message := <-ch:
             return message, nil
         default:
             return NodeMessage{}, fmt.Errorf("no messages available for node %s", nodeID)
         }
     }
     ```

3. **Implement worker pools using channels**
   - Create channel-based worker pool:

     ```go
     type ChannelWorkerPool struct {
         jobChan    chan Job
         resultChan chan Result
         workers    int
         quit       chan struct{}
         wg         sync.WaitGroup
     }
     
     func NewChannelWorkerPool(workers, jobBuffer, resultBuffer int) *ChannelWorkerPool {
         return &ChannelWorkerPool{
             jobChan:    make(chan Job, jobBuffer),
             resultChan: make(chan Result, resultBuffer),
             workers:    workers,
             quit:       make(chan struct{}),
         }
     }
     
     func (cwp *ChannelWorkerPool) Start() {
         for i := 0; i < cwp.workers; i++ {
             cwp.wg.Add(1)
             go cwp.worker(i)
         }
     }
     
     func (cwp *ChannelWorkerPool) worker(id int) {
         defer cwp.wg.Done()
         
         for {
             select {
             case job := <-cwp.jobChan:
                 result := cwp.processJob(job, id)
                 
                 select {
                 case cwp.resultChan <- result:
                 case <-cwp.quit:
                     return
                 }
                 
             case <-cwp.quit:
                 return
             }
         }
     }
     
     func (cwp *ChannelWorkerPool) processJob(job Job, workerID int) Result {
         start := time.Now()
         output, err := job.Node.Execute(job.Context, job.Input)
         duration := time.Since(start)
         
         return Result{
             JobID:    job.ID,
             Output:   output,
             Error:    err,
             WorkerID: workerID,
             Duration: duration,
         }
     }
     
     func (cwp *ChannelWorkerPool) Submit(job Job) {
         cwp.jobChan <- job
     }
     
     func (cwp *ChannelWorkerPool) Results() <-chan Result {
         return cwp.resultChan
     }
     
     func (cwp *ChannelWorkerPool) Stop() {
         close(cwp.quit)
         cwp.wg.Wait()
         close(cwp.jobChan)
         close(cwp.resultChan)
     }
     ```

4. **Add signaling patterns for graph synchronization**
   - Implement synchronization using channels:

     ```go
     type GraphSynchronizer struct {
         nodeSignals   map[string]chan struct{}
         barrierSignal chan struct{}
         readyNodes    map[string]bool
         mu            sync.Mutex
         totalNodes    int
     }
     
     func NewGraphSynchronizer(nodeIDs []string) *GraphSynchronizer {
         gs := &GraphSynchronizer{
             nodeSignals:   make(map[string]chan struct{}),
             barrierSignal: make(chan struct{}),
             readyNodes:    make(map[string]bool),
             totalNodes:    len(nodeIDs),
         }
         
         for _, nodeID := range nodeIDs {
             gs.nodeSignals[nodeID] = make(chan struct{}, 1)
             gs.readyNodes[nodeID] = false
         }
         
         return gs
     }
     
     func (gs *GraphSynchronizer) SignalNodeReady(nodeID string) {
         gs.mu.Lock()
         defer gs.mu.Unlock()
         
         if !gs.readyNodes[nodeID] {
             gs.readyNodes[nodeID] = true
             
             // Check if all nodes are ready
             allReady := true
             for _, ready := range gs.readyNodes {
                 if !ready {
                     allReady = false
                     break
                 }
             }
             
             if allReady {
                 close(gs.barrierSignal)
             }
         }
     }
     
     func (gs *GraphSynchronizer) WaitForAllNodes() {
         <-gs.barrierSignal
     }
     
     func (gs *GraphSynchronizer) SignalNode(nodeID string) {
         if ch, exists := gs.nodeSignals[nodeID]; exists {
             select {
             case ch <- struct{}{}:
             default:
                 // Channel is full, signal already sent
             }
         }
     }
     
     func (gs *GraphSynchronizer) WaitForSignal(nodeID string) {
         if ch, exists := gs.nodeSignals[nodeID]; exists {
             <-ch
         }
     }
     ```

5. **Create channel-based state distribution system**
   - Implement state distribution using channels:

     ```go
     type StateDistributor struct {
         subscribers map[string]chan StateUpdate
         updates     chan StateUpdate
         mu          sync.RWMutex
         quit        chan struct{}
         wg          sync.WaitGroup
     }
     
     type StateUpdate struct {
         Key       string
         Value     interface{}
         Operation StateOperation
         Timestamp time.Time
     }
     
     type StateOperation int
     
     const (
         OpSet StateOperation = iota
         OpDelete
         OpClear
     )
     
     func NewStateDistributor() *StateDistributor {
         sd := &StateDistributor{
             subscribers: make(map[string]chan StateUpdate),
             updates:     make(chan StateUpdate, 100),
             quit:        make(chan struct{}),
         }
         
         sd.wg.Add(1)
         go sd.distributeUpdates()
         
         return sd
     }
     
     func (sd *StateDistributor) Subscribe(nodeID string, bufferSize int) <-chan StateUpdate {
         sd.mu.Lock()
         defer sd.mu.Unlock()
         
         ch := make(chan StateUpdate, bufferSize)
         sd.subscribers[nodeID] = ch
         return ch
     }
     
     func (sd *StateDistributor) Unsubscribe(nodeID string) {
         sd.mu.Lock()
         defer sd.mu.Unlock()
         
         if ch, exists := sd.subscribers[nodeID]; exists {
             close(ch)
             delete(sd.subscribers, nodeID)
         }
     }
     
     func (sd *StateDistributor) PublishUpdate(update StateUpdate) {
         select {
         case sd.updates <- update:
         case <-sd.quit:
         }
     }
     
     func (sd *StateDistributor) distributeUpdates() {
         defer sd.wg.Done()
         
         for {
             select {
             case update := <-sd.updates:
                 sd.mu.RLock()
                 for nodeID, ch := range sd.subscribers {
                     select {
                     case ch <- update:
                     default:
                         // Subscriber channel is full, skip
                         log.Printf("Skipping update for node %s: channel full", nodeID)
                     }
                 }
                 sd.mu.RUnlock()
                 
             case <-sd.quit:
                 return
             }
         }
     }
     
     func (sd *StateDistributor) Stop() {
         close(sd.quit)
         sd.wg.Wait()
         
         sd.mu.Lock()
         defer sd.mu.Unlock()
         
         for nodeID, ch := range sd.subscribers {
             close(ch)
             delete(sd.subscribers, nodeID)
         }
         
         close(sd.updates)
     }
     ```

## Key Concepts

- **Channels**: Go's primary communication mechanism between goroutines
- **Communication**: Sharing data through message passing
- **Signaling**: Using channels for coordination and synchronization
- **Worker pools**: Pattern for managing concurrent workers
- **Synchronization**: Coordinating execution between goroutines

## Code Examples

### Basic Channel Operations

```go
// Unbuffered channel
ch := make(chan int)

// Buffered channel
buffered := make(chan string, 10)

// Sending and receiving
go func() {
    ch <- 42
}()

value := <-ch

// Channel closing and range
close(buffered)
for value := range buffered {
    fmt.Println(value)
}
```

### Select Statement

```go
func selectExample(ch1, ch2 chan int, quit chan struct{}) {
    for {
        select {
        case v1 := <-ch1:
            fmt.Printf("Received from ch1: %d\n", v1)
        case v2 := <-ch2:
            fmt.Printf("Received from ch2: %d\n", v2)
        case <-quit:
            fmt.Println("Quitting")
            return
        default:
            fmt.Println("No channels ready")
            time.Sleep(time.Millisecond * 100)
        }
    }
}
```

### Pipeline Pattern

```go
func pipeline() {
    // Stage 1: Generate numbers
    numbers := make(chan int)
    go func() {
        defer close(numbers)
        for i := 1; i <= 10; i++ {
            numbers <- i
        }
    }()
    
    // Stage 2: Square numbers
    squares := make(chan int)
    go func() {
        defer close(squares)
        for n := range numbers {
            squares <- n * n
        }
    }()
    
    // Stage 3: Print results
    for s := range squares {
        fmt.Println(s)
    }
}
```

## Resources

- Ultimate Go Programming 10.1-10.6 transcripts
- [Go Channels](https://tour.golang.org/concurrency/2)
- [Channel Patterns](https://blog.golang.org/pipelines)
- [Effective Go - Channels](https://golang.org/doc/effective_go#channels)

## Validation Checklist

- [ ] Channel examples from transcripts implemented
- [ ] Channel-based message passing created for LangGraph nodes
- [ ] Worker pools implemented using channels
- [ ] Signaling patterns added for graph synchronization
- [ ] Channel-based state distribution system created
- [ ] All channel operations tested for correctness and performance
