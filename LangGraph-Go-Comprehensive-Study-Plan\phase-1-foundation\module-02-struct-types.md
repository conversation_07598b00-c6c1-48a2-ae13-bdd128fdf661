# Module 2: Struct Types & Memory Layout

## Video Coverage

**2.2 Struct Types (23:27)**

## Learning Objectives

- Master struct declaration and memory alignment
- Understand data organization and layout
- Implement basic data structures for LangGraph

## Hands-on Tasks

1. **Analyze struct types from video transcript examples**
   - Study struct declaration syntax and patterns from the video
   - Understand field ordering and memory layout implications
   - Implement examples demonstrating struct initialization
   - Practice both named and anonymous struct patterns

2. **Implement `Node`, `Edge`, and `Graph` struct types**
   - Create core LangGraph data structures:

     ```go
     type Node struct {
         ID       string
         Type     NodeType
         Function func(state interface{}) (interface{}, error)
         Metadata map[string]interface{}
     }
     
     type Edge struct {
         From   string
         To     string
         Weight float64
     }
     
     type Graph struct {
         Nodes map[string]*Node
         Edges []Edge
         State interface{}
     }
     ```

   - Add proper field tags and documentation
   - Implement constructor functions for each type

3. **Create memory-efficient data layouts for LangGraph components**
   - Analyze memory usage of different field orderings
   - Optimize struct layouts for cache efficiency
   - Use tools like `go tool compile -S` to examine memory layout
   - Implement benchmarks to measure memory usage differences

4. **Implement struct embedding patterns for composition**
   - Create base types that can be embedded:

     ```go
     type BaseNode struct {
         ID       string
         Metadata map[string]interface{}
     }
     
     type ProcessingNode struct {
         BaseNode
         ProcessFunc func(interface{}) interface{}
     }
     ```

   - Demonstrate method promotion through embedding
   - Show composition vs inheritance patterns

5. **Add memory alignment considerations for performance**
   - Use `unsafe.Sizeof()` and `unsafe.Alignof()` to analyze structures
   - Implement padding-aware struct designs
   - Create examples showing performance impact of alignment
   - Add documentation about memory layout decisions

## Key Concepts

- **Struct types**: Custom data types that group related data
- **Memory alignment**: How Go aligns struct fields in memory
- **Data organization**: Efficient organization of data for performance
- **Composition**: Building complex types from simpler ones through embedding

## Prerequisites for Next Module

- Understanding of struct declaration and initialization
- Basic knowledge of memory layout and alignment
- Core LangGraph data structures implemented
- Familiarity with struct embedding patterns

## Code Examples

### Basic Struct Declaration

```go
type Person struct {
    Name string
    Age  int
}

// Zero value initialization
var p Person

// Literal initialization
p2 := Person{
    Name: "Alice",
    Age:  30,
}
```

### Memory Layout Analysis

```go
package main

import (
    "fmt"
    "unsafe"
)

type BadLayout struct {
    a bool   // 1 byte + 7 bytes padding
    b int64  // 8 bytes
    c bool   // 1 byte + 7 bytes padding
}

type GoodLayout struct {
    b int64  // 8 bytes
    a bool   // 1 byte
    c bool   // 1 byte + 6 bytes padding
}

func main() {
    fmt.Printf("BadLayout size: %d\n", unsafe.Sizeof(BadLayout{}))   // 24 bytes
    fmt.Printf("GoodLayout size: %d\n", unsafe.Sizeof(GoodLayout{})) // 16 bytes
}
```

## Resources

- Ultimate Go Programming 2.2 Struct Types transcript
- [Go Memory Layout](https://go101.org/article/memory-layout.html)
- [Struct Field Alignment](https://dave.cheney.net/2015/10/09/padding-is-hard)
- [Go by Example - Structs](https://gobyexample.com/structs)

## Validation Checklist

- [ ] Core LangGraph struct types implemented
- [ ] Memory layout analysis completed
- [ ] Struct embedding patterns demonstrated
- [ ] Performance benchmarks for different layouts
- [ ] Documentation for memory alignment decisions
