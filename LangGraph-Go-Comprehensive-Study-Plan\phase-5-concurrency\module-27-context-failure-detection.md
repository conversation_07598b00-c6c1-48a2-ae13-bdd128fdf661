# Module 27: Context & Failure Detection

## Video Coverage

**11.1-11.2 Context Parts 1-2 (27:47 total), Failure Detection (23:17)**

## Learning Objectives

- Master context propagation and failure handling
- Understand timeout and cancellation mechanisms
- Implement LangGraph's context management and error propagation

## Hands-on Tasks

1. **Implement context examples from video transcripts**
   - Study context creation and propagation
   - Practice timeout and cancellation patterns
   - Understand context values and request scoping
   - Implement context-aware operations

2. **Add context propagation to LangGraph execution**
   - Implement context-aware graph execution:

     ```go
     type ContextAwareGraph struct {
         *Graph
         defaultTimeout time.Duration
         contextValues  map[string]interface{}
         mu             sync.RWMutex
     }
     
     func NewContextAwareGraph(id string, timeout time.Duration) *ContextAwareGraph {
         return &ContextAwareGraph{
             Graph:          NewGraph(id),
             defaultTimeout: timeout,
             contextValues:  make(map[string]interface{}),
         }
     }
     
     func (cag *ContextAwareGraph) ExecuteWithContext(ctx context.Context) (*ExecutionResult, error) {
         // Create execution context with timeout
         execCtx, cancel := context.WithTimeout(ctx, cag.defaultTimeout)
         defer cancel()
         
         // Add graph-specific values to context
         execCtx = cag.enrichContext(execCtx)
         
         result := &ExecutionResult{
             GraphID:   cag.ID(),
             StartTime: time.Now(),
             Outputs:   make(map[string]interface{}),
         }
         
         // Execute nodes with context propagation
         for nodeID, node := range cag.nodes {
             select {
             case <-execCtx.Done():
                 result.Error = execCtx.Err()
                 return result, execCtx.Err()
             default:
                 output, err := cag.executeNodeWithContext(execCtx, node)
                 if err != nil {
                     result.Error = err
                     return result, err
                 }
                 result.Outputs[nodeID] = output
             }
         }
         
         result.EndTime = time.Now()
         return result, nil
     }
     
     func (cag *ContextAwareGraph) enrichContext(ctx context.Context) context.Context {
         cag.mu.RLock()
         defer cag.mu.RUnlock()
         
         enrichedCtx := ctx
         for key, value := range cag.contextValues {
             enrichedCtx = context.WithValue(enrichedCtx, key, value)
         }
         
         return enrichedCtx
     }
     
     func (cag *ContextAwareGraph) executeNodeWithContext(ctx context.Context, node *Node) (interface{}, error) {
         // Create node-specific context
         nodeCtx := context.WithValue(ctx, "node_id", node.ID())
         nodeCtx = context.WithValue(nodeCtx, "execution_time", time.Now())
         
         return node.Execute(nodeCtx, nil)
     }
     
     func (cag *ContextAwareGraph) SetContextValue(key string, value interface{}) {
         cag.mu.Lock()
         defer cag.mu.Unlock()
         cag.contextValues[key] = value
     }
     ```

3. **Implement timeout and cancellation for long-running nodes**
   - Create timeout management system:

     ```go
     type TimeoutManager struct {
         activeTimeouts map[string]*TimeoutInfo
         mu             sync.RWMutex
         cleanupTicker  *time.Ticker
         done           chan struct{}
     }
     
     type TimeoutInfo struct {
         NodeID    string
         StartTime time.Time
         Timeout   time.Duration
         Cancel    context.CancelFunc
         Callback  func(string, error)
     }
     
     func NewTimeoutManager(cleanupInterval time.Duration) *TimeoutManager {
         tm := &TimeoutManager{
             activeTimeouts: make(map[string]*TimeoutInfo),
             cleanupTicker:  time.NewTicker(cleanupInterval),
             done:           make(chan struct{}),
         }
         
         go tm.cleanupLoop()
         return tm
     }
     
     func (tm *TimeoutManager) ExecuteWithTimeout(
         nodeID string,
         timeout time.Duration,
         fn func(context.Context) (interface{}, error),
         callback func(string, error),
     ) (interface{}, error) {
         ctx, cancel := context.WithTimeout(context.Background(), timeout)
         
         info := &TimeoutInfo{
             NodeID:    nodeID,
             StartTime: time.Now(),
             Timeout:   timeout,
             Cancel:    cancel,
             Callback:  callback,
         }
         
         tm.mu.Lock()
         tm.activeTimeouts[nodeID] = info
         tm.mu.Unlock()
         
         defer func() {
             tm.mu.Lock()
             delete(tm.activeTimeouts, nodeID)
             tm.mu.Unlock()
             cancel()
         }()
         
         // Execute with timeout
         resultChan := make(chan interface{}, 1)
         errorChan := make(chan error, 1)
         
         go func() {
             result, err := fn(ctx)
             if err != nil {
                 errorChan <- err
             } else {
                 resultChan <- result
             }
         }()
         
         select {
         case result := <-resultChan:
             return result, nil
         case err := <-errorChan:
             return nil, err
         case <-ctx.Done():
             err := ctx.Err()
             if callback != nil {
                 callback(nodeID, err)
             }
             return nil, err
         }
     }
     
     func (tm *TimeoutManager) CancelNode(nodeID string) bool {
         tm.mu.RLock()
         info, exists := tm.activeTimeouts[nodeID]
         tm.mu.RUnlock()
         
         if exists {
             info.Cancel()
             return true
         }
         return false
     }
     
     func (tm *TimeoutManager) cleanupLoop() {
         for {
             select {
             case <-tm.cleanupTicker.C:
                 tm.cleanupExpiredTimeouts()
             case <-tm.done:
                 return
             }
         }
     }
     
     func (tm *TimeoutManager) cleanupExpiredTimeouts() {
         now := time.Now()
         
         tm.mu.Lock()
         defer tm.mu.Unlock()
         
         for nodeID, info := range tm.activeTimeouts {
             if now.Sub(info.StartTime) > info.Timeout {
                 info.Cancel()
                 if info.Callback != nil {
                     go info.Callback(nodeID, context.DeadlineExceeded)
                 }
                 delete(tm.activeTimeouts, nodeID)
             }
         }
     }
     ```

4. **Create failure detection and recovery mechanisms**
   - Implement comprehensive failure detection:

     ```go
     type FailureDetector struct {
         healthChecks   map[string]*HealthCheck
         failureHistory map[string][]FailureEvent
         mu             sync.RWMutex
         checkInterval  time.Duration
         maxHistory     int
         ticker         *time.Ticker
         done           chan struct{}
     }
     
     type HealthCheck struct {
         NodeID      string
         CheckFunc   func(context.Context) error
         Threshold   int
         Failures    int
         LastCheck   time.Time
         Status      HealthStatus
         Recovery    func() error
     }
     
     type HealthStatus int
     
     const (
         HealthyStatus HealthStatus = iota
         DegradedStatus
         UnhealthyStatus
         RecoveringStatus
     )
     
     type FailureEvent struct {
         NodeID    string
         Error     error
         Timestamp time.Time
         Severity  FailureSeverity
     }
     
     type FailureSeverity int
     
     const (
         MinorFailure FailureSeverity = iota
         MajorFailure
         CriticalFailure
     )
     
     func NewFailureDetector(checkInterval time.Duration, maxHistory int) *FailureDetector {
         fd := &FailureDetector{
             healthChecks:   make(map[string]*HealthCheck),
             failureHistory: make(map[string][]FailureEvent),
             checkInterval:  checkInterval,
             maxHistory:     maxHistory,
             ticker:         time.NewTicker(checkInterval),
             done:           make(chan struct{}),
         }
         
         go fd.monitorHealth()
         return fd
     }
     
     func (fd *FailureDetector) RegisterHealthCheck(
         nodeID string,
         checkFunc func(context.Context) error,
         threshold int,
         recovery func() error,
     ) {
         fd.mu.Lock()
         defer fd.mu.Unlock()
         
         fd.healthChecks[nodeID] = &HealthCheck{
             NodeID:    nodeID,
             CheckFunc: checkFunc,
             Threshold: threshold,
             Recovery:  recovery,
             Status:    HealthyStatus,
         }
     }
     
     func (fd *FailureDetector) monitorHealth() {
         for {
             select {
             case <-fd.ticker.C:
                 fd.performHealthChecks()
             case <-fd.done:
                 return
             }
         }
     }
     
     func (fd *FailureDetector) performHealthChecks() {
         fd.mu.Lock()
         defer fd.mu.Unlock()
         
         for nodeID, check := range fd.healthChecks {
             ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
             
             err := check.CheckFunc(ctx)
             check.LastCheck = time.Now()
             
             if err != nil {
                 check.Failures++
                 fd.recordFailure(nodeID, err, fd.determineSeverity(check.Failures))
                 
                 if check.Failures >= check.Threshold {
                     check.Status = UnhealthyStatus
                     fd.attemptRecovery(nodeID, check)
                 } else {
                     check.Status = DegradedStatus
                 }
             } else {
                 if check.Status == RecoveringStatus || check.Status == DegradedStatus {
                     check.Status = HealthyStatus
                 }
                 check.Failures = 0
             }
             
             cancel()
         }
     }
     
     func (fd *FailureDetector) recordFailure(nodeID string, err error, severity FailureSeverity) {
         event := FailureEvent{
             NodeID:    nodeID,
             Error:     err,
             Timestamp: time.Now(),
             Severity:  severity,
         }
         
         history := fd.failureHistory[nodeID]
         history = append(history, event)
         
         if len(history) > fd.maxHistory {
             history = history[1:]
         }
         
         fd.failureHistory[nodeID] = history
     }
     
     func (fd *FailureDetector) attemptRecovery(nodeID string, check *HealthCheck) {
         if check.Recovery == nil {
             return
         }
         
         check.Status = RecoveringStatus
         
         go func() {
             if err := check.Recovery(); err != nil {
                 fd.mu.Lock()
                 fd.recordFailure(nodeID, fmt.Errorf("recovery failed: %w", err), CriticalFailure)
                 fd.mu.Unlock()
             }
         }()
     }
     
     func (fd *FailureDetector) determineSeverity(failures int) FailureSeverity {
         switch {
         case failures <= 2:
             return MinorFailure
         case failures <= 5:
             return MajorFailure
         default:
             return CriticalFailure
         }
     }
     
     func (fd *FailureDetector) GetNodeHealth(nodeID string) (HealthStatus, bool) {
         fd.mu.RLock()
         defer fd.mu.RUnlock()
         
         check, exists := fd.healthChecks[nodeID]
         if !exists {
             return HealthyStatus, false
         }
         
         return check.Status, true
     }
     
     func (fd *FailureDetector) GetFailureHistory(nodeID string) []FailureEvent {
         fd.mu.RLock()
         defer fd.mu.RUnlock()
         
         history := fd.failureHistory[nodeID]
         result := make([]FailureEvent, len(history))
         copy(result, history)
         return result
     }
     ```

5. **Add distributed tracing support using context**
   - Implement distributed tracing:

     ```go
     type TraceContext struct {
         TraceID  string
         SpanID   string
         ParentID string
         Baggage  map[string]string
     }
     
     type Tracer struct {
         serviceName string
         spans       map[string]*Span
         mu          sync.RWMutex
     }
     
     type Span struct {
         TraceID     string
         SpanID      string
         ParentID    string
         OperationName string
         StartTime   time.Time
         EndTime     time.Time
         Tags        map[string]interface{}
         Logs        []LogEntry
         Finished    bool
     }
     
     type LogEntry struct {
         Timestamp time.Time
         Fields    map[string]interface{}
     }
     
     func NewTracer(serviceName string) *Tracer {
         return &Tracer{
             serviceName: serviceName,
             spans:       make(map[string]*Span),
         }
     }
     
     func (t *Tracer) StartSpan(ctx context.Context, operationName string) (context.Context, *Span) {
         traceCtx := t.extractTraceContext(ctx)
         
         span := &Span{
             TraceID:       traceCtx.TraceID,
             SpanID:        t.generateSpanID(),
             ParentID:      traceCtx.SpanID,
             OperationName: operationName,
             StartTime:     time.Now(),
             Tags:          make(map[string]interface{}),
             Logs:          make([]LogEntry, 0),
         }
         
         if span.TraceID == "" {
             span.TraceID = t.generateTraceID()
         }
         
         t.mu.Lock()
         t.spans[span.SpanID] = span
         t.mu.Unlock()
         
         newTraceCtx := TraceContext{
             TraceID:  span.TraceID,
             SpanID:   span.SpanID,
             ParentID: span.ParentID,
             Baggage:  traceCtx.Baggage,
         }
         
         newCtx := context.WithValue(ctx, "trace_context", newTraceCtx)
         return newCtx, span
     }
     
     func (t *Tracer) extractTraceContext(ctx context.Context) TraceContext {
         if traceCtx, ok := ctx.Value("trace_context").(TraceContext); ok {
             return traceCtx
         }
         
         return TraceContext{
             Baggage: make(map[string]string),
         }
     }
     
     func (t *Tracer) generateTraceID() string {
         return fmt.Sprintf("trace_%d_%d", time.Now().UnixNano(), rand.Int63())
     }
     
     func (t *Tracer) generateSpanID() string {
         return fmt.Sprintf("span_%d_%d", time.Now().UnixNano(), rand.Int63())
     }
     
     func (s *Span) SetTag(key string, value interface{}) {
         s.Tags[key] = value
     }
     
     func (s *Span) LogFields(fields map[string]interface{}) {
         entry := LogEntry{
             Timestamp: time.Now(),
             Fields:    fields,
         }
         s.Logs = append(s.Logs, entry)
     }
     
     func (s *Span) Finish() {
         s.EndTime = time.Now()
         s.Finished = true
     }
     ```

## Key Concepts

- **Context**: Request-scoped values and cancellation
- **Timeouts**: Time-based operation limits
- **Cancellation**: Graceful operation termination
- **Failure detection**: Monitoring and health checking
- **Distributed tracing**: Request tracking across services

## Resources

- Ultimate Go Programming 11.1-11.2 transcripts
- [Go Context Package](https://golang.org/pkg/context/)
- [Context Best Practices](https://blog.golang.org/context)

## Validation Checklist

- [ ] Context examples from transcripts implemented
- [ ] Context propagation added to LangGraph execution
- [ ] Timeout and cancellation implemented for long-running nodes
- [ ] Failure detection and recovery mechanisms created
- [ ] Distributed tracing support added using context
- [ ] All context operations tested for proper propagation and cleanup
