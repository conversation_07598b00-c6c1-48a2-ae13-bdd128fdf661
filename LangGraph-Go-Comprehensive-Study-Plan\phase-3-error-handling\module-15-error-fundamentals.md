# Module 15: Go Error Handling Fundamentals

## Video Coverage

**6.1-6.3 Default Error Values (11:33), Error Variables (2:40), Type as Context (7:04)**

## Learning Objectives

- <PERSON> Go's error handling philosophy
- Understand error types and context
- Implement LangGraph error handling system

## Hands-on Tasks

1. **Implement error handling examples from video transcripts**
   - Study Go's error interface and built-in error types
   - Practice creating custom error types
   - Understand error values and nil error handling
   - Implement examples showing different error patterns

2. **Create comprehensive error types for LangGraph operations**
   - Design error hierarchy for LangGraph:

     ```go
     // Base error types
     type GraphError struct {
         Op      string    // Operation that failed
         GraphID string    // Graph identifier
         Err     error     // Underlying error
         Time    time.Time // When error occurred
     }
     
     func (e *GraphError) Error() string {
         return fmt.Sprintf("graph %s: %s: %v", e.GraphID, e.Op, e.Err)
     }
     
     func (e *GraphError) Unwrap() error {
         return e.Err
     }
     
     // Specific error types
     type NodeError struct {
         GraphError
         NodeID string
         Phase  ExecutionPhase
     }
     
     func (e *NodeError) Error() string {
         return fmt.Sprintf("node %s in graph %s during %s: %s: %v", 
             e.NodeID, e.GraphID, e.Phase, e.Op, e.Err)
     }
     
     type ValidationError struct {
         GraphError
         Field   string
         Value   interface{}
         Reason  string
     }
     
     func (e *ValidationError) Error() string {
         return fmt.Sprintf("validation failed for %s=%v: %s", e.Field, e.Value, e.Reason)
     }
     ```

3. **Implement error wrapping and context preservation**
   - Create error wrapping utilities:

     ```go
     func WrapNodeError(nodeID string, op string, err error) error {
         if err == nil {
             return nil
         }
         return &NodeError{
             GraphError: GraphError{
                 Op:   op,
                 Err:  err,
                 Time: time.Now(),
             },
             NodeID: nodeID,
         }
     }
     
     func WrapValidationError(field string, value interface{}, reason string, err error) error {
         return &ValidationError{
             GraphError: GraphError{
                 Op:   "validation",
                 Err:  err,
                 Time: time.Now(),
             },
             Field:  field,
             Value:  value,
             Reason: reason,
         }
     }
     
     // Error context preservation
     type ErrorContext struct {
         GraphID     string
         NodeID      string
         ExecutionID string
         Metadata    map[string]interface{}
     }
     
     func (ec *ErrorContext) WrapError(op string, err error) error {
         if err == nil {
             return nil
         }
         
         wrapped := &GraphError{
             Op:      op,
             GraphID: ec.GraphID,
             Err:     err,
             Time:    time.Now(),
         }
         
         if ec.NodeID != "" {
             return &NodeError{
                 GraphError: *wrapped,
                 NodeID:     ec.NodeID,
             }
         }
         
         return wrapped
     }
     ```

4. **Add error handling to all LangGraph components**
   - Implement consistent error handling patterns:

     ```go
     func (g *Graph) AddNode(node *Node) error {
         if node == nil {
             return &ValidationError{
                 GraphError: GraphError{
                     Op:      "add_node",
                     GraphID: g.ID(),
                     Time:    time.Now(),
                 },
                 Field:  "node",
                 Value:  nil,
                 Reason: "node cannot be nil",
             }
         }
         
         if node.ID() == "" {
             return &ValidationError{
                 GraphError: GraphError{
                     Op:      "add_node",
                     GraphID: g.ID(),
                     Time:    time.Now(),
                 },
                 Field:  "node.id",
                 Value:  "",
                 Reason: "node ID cannot be empty",
             }
         }
         
         if _, exists := g.nodes[node.ID()]; exists {
             return &NodeError{
                 GraphError: GraphError{
                     Op:      "add_node",
                     GraphID: g.ID(),
                     Err:     fmt.Errorf("node already exists"),
                     Time:    time.Now(),
                 },
                 NodeID: node.ID(),
             }
         }
         
         g.nodes[node.ID()] = node
         return nil
     }
     
     func (n *Node) Execute(ctx context.Context, input interface{}) (interface{}, error) {
         if n.function == nil {
             return nil, &NodeError{
                 GraphError: GraphError{
                     Op:   "execute",
                     Err:  fmt.Errorf("no execution function defined"),
                     Time: time.Now(),
                 },
                 NodeID: n.ID(),
                 Phase:  PhaseExecution,
             }
         }
         
         output, err := n.function(input)
         if err != nil {
             return nil, WrapNodeError(n.ID(), "execute", err)
         }
         
         return output, nil
     }
     ```

5. **Create error documentation and handling guides**
   - Document error handling patterns and best practices:

     ```go
     // Error handling guidelines for LangGraph
     
     // 1. Always check for nil before wrapping errors
     func safeWrap(op string, err error) error {
         if err == nil {
             return nil
         }
         return fmt.Errorf("%s: %w", op, err)
     }
     
     // 2. Use sentinel errors for expected conditions
     var (
         ErrNodeNotFound    = errors.New("node not found")
         ErrCyclicGraph     = errors.New("cyclic dependency detected")
         ErrInvalidInput    = errors.New("invalid input")
         ErrExecutionFailed = errors.New("execution failed")
     )
     
     // 3. Provide error checking utilities
     func IsNodeError(err error) bool {
         var nodeErr *NodeError
         return errors.As(err, &nodeErr)
     }
     
     func IsValidationError(err error) bool {
         var validationErr *ValidationError
         return errors.As(err, &validationErr)
     }
     
     func GetNodeID(err error) string {
         var nodeErr *NodeError
         if errors.As(err, &nodeErr) {
             return nodeErr.NodeID
         }
         return ""
     }
     ```

## Key Concepts

- **Error handling**: Go's explicit error handling philosophy
- **Error types**: Custom error types with context information
- **Context**: Preserving error context through the call stack
- **Go philosophy**: Errors are values, handle them explicitly

## Code Examples

### Basic Error Handling

```go
// Simple error creation
func divide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, errors.New("division by zero")
    }
    return a / b, nil
}

// Error checking
result, err := divide(10, 0)
if err != nil {
    log.Printf("Error: %v", err)
    return
}
fmt.Printf("Result: %f", result)
```

### Custom Error Types

```go
type MathError struct {
    Op   string
    Args []float64
    Err  error
}

func (e *MathError) Error() string {
    return fmt.Sprintf("math operation %s with args %v: %v", e.Op, e.Args, e.Err)
}

func (e *MathError) Unwrap() error {
    return e.Err
}

func safeDivide(a, b float64) (float64, error) {
    if b == 0 {
        return 0, &MathError{
            Op:   "divide",
            Args: []float64{a, b},
            Err:  errors.New("division by zero"),
        }
    }
    return a / b, nil
}
```

### Error Wrapping and Unwrapping

```go
func processFile(filename string) error {
    data, err := ioutil.ReadFile(filename)
    if err != nil {
        return fmt.Errorf("failed to read file %s: %w", filename, err)
    }
    
    if err := validateData(data); err != nil {
        return fmt.Errorf("validation failed for %s: %w", filename, err)
    }
    
    return nil
}

// Error checking with unwrapping
err := processFile("config.json")
if err != nil {
    // Check for specific error types
    if errors.Is(err, os.ErrNotExist) {
        log.Println("File does not exist")
    }
    
    // Extract wrapped errors
    var pathErr *os.PathError
    if errors.As(err, &pathErr) {
        log.Printf("Path error: %s", pathErr.Path)
    }
}
```

## Resources

- Ultimate Go Programming 6.1-6.3 transcripts
- [Error Handling in Go](https://blog.golang.org/error-handling-and-go)
- [Working with Errors in Go 1.13](https://blog.golang.org/go1.13-errors)
- [Go by Example - Errors](https://gobyexample.com/errors)

## Validation Checklist

- [ ] Error handling examples from transcripts implemented
- [ ] Comprehensive error types created for LangGraph operations
- [ ] Error wrapping and context preservation implemented
- [ ] Error handling added to all LangGraph components
- [ ] Error documentation and handling guides created
- [ ] Sentinel errors defined for common conditions
