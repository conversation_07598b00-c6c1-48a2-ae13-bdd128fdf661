# Module 7: Slices & Dynamic Data Structures

## Video Coverage

**3.3 Slices Parts 1-6 (8:46, 15:32, 11:45, 5:51, 8:29, 4:35)**

## Learning Objectives

- Master slice mechanics and growth patterns
- Understand memory management for dynamic collections
- Implement dynamic collections for LangGraph components

## Hands-on Tasks

1. **Implement all slice examples from video transcripts**
   - Create slices using make() and literal syntax
   - Demonstrate slice header structure (pointer, length, capacity)
   - Show slice growth and reallocation behavior
   - Practice slice operations: append, copy, slicing

2. **Create dynamic node and edge collections using slices**
   - Implement growable collections for graph components:

     ```go
     type DynamicGraph struct {
         nodes []Node
         edges []Edge
         nodeIndex map[string]int  // ID to slice index mapping
     }
     
     func (g *DynamicGraph) AddNode(node Node) {
         g.nodes = append(g.nodes, node)
         g.nodeIndex[node.ID] = len(g.nodes) - 1
     }
     
     func (g *DynamicGraph) RemoveNode(id string) error {
         index, exists := g.nodeIndex[id]
         if !exists {
             return fmt.Errorf("node %s not found", id)
         }
         
         // Remove from slice (order-preserving)
         copy(g.nodes[index:], g.nodes[index+1:])
         g.nodes = g.nodes[:len(g.nodes)-1]
         
         // Update index map
         delete(g.nodeIndex, id)
         for nodeID, idx := range g.nodeIndex {
             if idx > index {
                 g.nodeIndex[nodeID] = idx - 1
             }
         }
         return nil
     }
     ```

3. **Implement efficient slice growth strategies for graph expansion**
   - Pre-allocate slices with appropriate capacity
   - Implement custom growth algorithms
   - Use slice pools for frequent allocations
   - Create memory-efficient append patterns

4. **Add slice-based queues and stacks for graph algorithms**
   - Implement FIFO queue for breadth-first search:

     ```go
     type NodeQueue struct {
         items []string
         front int
     }
     
     func (q *NodeQueue) Enqueue(item string) {
         q.items = append(q.items, item)
     }
     
     func (q *NodeQueue) Dequeue() (string, bool) {
         if q.front >= len(q.items) {
             return "", false
         }
         item := q.items[q.front]
         q.front++
         
         // Periodically reset to avoid memory leak
         if q.front > len(q.items)/2 {
             copy(q.items, q.items[q.front:])
             q.items = q.items[:len(q.items)-q.front]
             q.front = 0
         }
         return item, true
     }
     ```

5. **Optimize slice operations for LangGraph execution patterns**
   - Profile slice operations in graph algorithms
   - Implement zero-allocation slice patterns where possible
   - Use slice tricks for efficient operations
   - Create benchmarks for different slice strategies

## Key Concepts

- **Slices**: Dynamic arrays with length and capacity
- **Dynamic arrays**: Growable data structures
- **Memory management**: Understanding slice growth and reallocation
- **Growth patterns**: Strategies for efficient slice expansion

## Code Examples

### Slice Basics

```go
// Creating slices
var s1 []int                    // nil slice
s2 := make([]int, 5)           // length 5, capacity 5
s3 := make([]int, 3, 10)       // length 3, capacity 10
s4 := []int{1, 2, 3, 4, 5}     // slice literal

// Slice operations
s4 = append(s4, 6, 7, 8)       // append elements
s5 := s4[1:4]                  // slice of slice
copy(s2, s4)                   // copy elements
```

### Slice Growth Analysis

```go
func analyzeSliceGrowth() {
    var s []int
    
    for i := 0; i < 20; i++ {
        prevCap := cap(s)
        s = append(s, i)
        if cap(s) != prevCap {
            fmt.Printf("Length: %d, Capacity: %d (grew from %d)\n", 
                len(s), cap(s), prevCap)
        }
    }
}
```

### Efficient Slice Patterns

```go
// Pre-allocate with known capacity
func efficientAppend(items []string) []string {
    result := make([]string, 0, len(items)*2) // pre-allocate
    for _, item := range items {
        result = append(result, strings.ToUpper(item))
        result = append(result, strings.ToLower(item))
    }
    return result
}

// Zero-allocation filtering
func filterInPlace(slice []int, predicate func(int) bool) []int {
    n := 0
    for _, item := range slice {
        if predicate(item) {
            slice[n] = item
            n++
        }
    }
    return slice[:n]
}
```

### Slice-based Stack

```go
type Stack struct {
    items []string
}

func (s *Stack) Push(item string) {
    s.items = append(s.items, item)
}

func (s *Stack) Pop() (string, bool) {
    if len(s.items) == 0 {
        return "", false
    }
    
    index := len(s.items) - 1
    item := s.items[index]
    s.items = s.items[:index]
    return item, true
}

func (s *Stack) Peek() (string, bool) {
    if len(s.items) == 0 {
        return "", false
    }
    return s.items[len(s.items)-1], true
}
```

## Resources

- Ultimate Go Programming 3.3 Slices Parts 1-6 transcripts
- [Go Slices: usage and internals](https://blog.golang.org/slices-intro)
- [Slice Tricks](https://github.com/golang/go/wiki/SliceTricks)
- [Go by Example - Slices](https://gobyexample.com/slices)

## Validation Checklist

- [ ] All slice examples from transcripts implemented
- [ ] Dynamic graph collections created with slices
- [ ] Efficient growth strategies implemented
- [ ] Queue and stack data structures built
- [ ] Slice operations optimized and benchmarked
