# Module 24: Data Races & Synchronization

## Video Coverage

**9.1-9.6 Cache Coherency (12:39), Atomic Functions (11:30), Mutexes (14:38), Race Detection (4:48), Map Data Race (4:01), Interface-Based Race Condition (8:14)**

## Learning Objectives

- Master data race detection and synchronization primitives
- Understand memory consistency and atomic operations
- Implement thread-safe LangGraph components

## Hands-on Tasks

1. **Implement synchronization examples from video transcripts**
   - Study cache coherency and memory consistency models
   - Practice atomic operations and compare-and-swap
   - Understand mutex mechanics and lock contention
   - Implement race detection and debugging techniques

2. **Add thread-safe state management to LangGraph**
   - Create thread-safe state store:

     ```go
     type ThreadSafeStateStore struct {
         data map[string]interface{}
         mu   sync.RWMutex
     }
     
     func NewThreadSafeStateStore() *ThreadSafeStateStore {
         return &ThreadSafeStateStore{
             data: make(map[string]interface{}),
         }
     }
     
     func (ts *ThreadSafeStateStore) Get(key string) (interface{}, bool) {
         ts.mu.RLock()
         defer ts.mu.RUnlock()
         
         value, exists := ts.data[key]
         return value, exists
     }
     
     func (ts *ThreadSafeStateStore) Set(key string, value interface{}) {
         ts.mu.Lock()
         defer ts.mu.Unlock()
         
         ts.data[key] = value
     }
     
     func (ts *ThreadSafeStateStore) Delete(key string) {
         ts.mu.Lock()
         defer ts.mu.Unlock()
         
         delete(ts.data, key)
     }
     
     func (ts *ThreadSafeStateStore) GetAll() map[string]interface{} {
         ts.mu.RLock()
         defer ts.mu.RUnlock()
         
         // Create a copy to avoid race conditions
         result := make(map[string]interface{})
         for k, v := range ts.data {
             result[k] = v
         }
         return result
     }
     ```

3. **Implement atomic operations for graph statistics**
   - Create atomic counters and statistics:

     ```go
     type GraphStatistics struct {
         nodesExecuted    int64
         totalExecutions  int64
         successfulRuns   int64
         failedRuns       int64
         totalDuration    int64 // nanoseconds
         lastExecution    int64 // unix timestamp
     }
     
     func (gs *GraphStatistics) IncrementNodesExecuted() {
         atomic.AddInt64(&gs.nodesExecuted, 1)
     }
     
     func (gs *GraphStatistics) IncrementTotalExecutions() {
         atomic.AddInt64(&gs.totalExecutions, 1)
     }
     
     func (gs *GraphStatistics) IncrementSuccessfulRuns() {
         atomic.AddInt64(&gs.successfulRuns, 1)
     }
     
     func (gs *GraphStatistics) IncrementFailedRuns() {
         atomic.AddInt64(&gs.failedRuns, 1)
     }
     
     func (gs *GraphStatistics) AddDuration(duration time.Duration) {
         atomic.AddInt64(&gs.totalDuration, int64(duration))
     }
     
     func (gs *GraphStatistics) UpdateLastExecution() {
         atomic.StoreInt64(&gs.lastExecution, time.Now().Unix())
     }
     
     func (gs *GraphStatistics) GetStats() map[string]int64 {
         return map[string]int64{
             "nodes_executed":    atomic.LoadInt64(&gs.nodesExecuted),
             "total_executions":  atomic.LoadInt64(&gs.totalExecutions),
             "successful_runs":   atomic.LoadInt64(&gs.successfulRuns),
             "failed_runs":       atomic.LoadInt64(&gs.failedRuns),
             "total_duration_ns": atomic.LoadInt64(&gs.totalDuration),
             "last_execution":    atomic.LoadInt64(&gs.lastExecution),
         }
     }
     
     func (gs *GraphStatistics) GetAverageDuration() time.Duration {
         totalExecs := atomic.LoadInt64(&gs.totalExecutions)
         if totalExecs == 0 {
             return 0
         }
         
         totalDur := atomic.LoadInt64(&gs.totalDuration)
         return time.Duration(totalDur / totalExecs)
     }
     ```

4. **Create mutex-protected critical sections**
   - Implement complex synchronization patterns:

     ```go
     type GraphExecutionCoordinator struct {
         activeNodes    map[string]*NodeExecution
         nodeQueue      []*Node
         completedNodes map[string]*NodeResult
         mu             sync.RWMutex
         executionMu    sync.Mutex
         condition      *sync.Cond
         maxConcurrent  int
         currentActive  int
     }
     
     type NodeExecution struct {
         Node      *Node
         StartTime time.Time
         Status    ExecutionStatus
         Cancel    context.CancelFunc
     }
     
     type ExecutionStatus int
     
     const (
         StatusPending ExecutionStatus = iota
         StatusRunning
         StatusCompleted
         StatusFailed
         StatusCancelled
     )
     
     func NewGraphExecutionCoordinator(maxConcurrent int) *GraphExecutionCoordinator {
         gec := &GraphExecutionCoordinator{
             activeNodes:    make(map[string]*NodeExecution),
             nodeQueue:      make([]*Node, 0),
             completedNodes: make(map[string]*NodeResult),
             maxConcurrent:  maxConcurrent,
         }
         gec.condition = sync.NewCond(&gec.executionMu)
         return gec
     }
     
     func (gec *GraphExecutionCoordinator) ScheduleNode(node *Node) {
         gec.mu.Lock()
         defer gec.mu.Unlock()
         
         gec.nodeQueue = append(gec.nodeQueue, node)
         gec.condition.Signal()
     }
     
     func (gec *GraphExecutionCoordinator) ExecuteNext() *Node {
         gec.executionMu.Lock()
         defer gec.executionMu.Unlock()
         
         // Wait for available slot or queued node
         for gec.currentActive >= gec.maxConcurrent || len(gec.nodeQueue) == 0 {
             gec.condition.Wait()
         }
         
         // Get next node from queue
         gec.mu.Lock()
         if len(gec.nodeQueue) == 0 {
             gec.mu.Unlock()
             return nil
         }
         
         node := gec.nodeQueue[0]
         gec.nodeQueue = gec.nodeQueue[1:]
         gec.mu.Unlock()
         
         // Mark as active
         ctx, cancel := context.WithCancel(context.Background())
         execution := &NodeExecution{
             Node:      node,
             StartTime: time.Now(),
             Status:    StatusRunning,
             Cancel:    cancel,
         }
         
         gec.activeNodes[node.ID()] = execution
         gec.currentActive++
         
         return node
     }
     
     func (gec *GraphExecutionCoordinator) CompleteNode(nodeID string, result *NodeResult) {
         gec.executionMu.Lock()
         defer gec.executionMu.Unlock()
         
         // Update active nodes
         if execution, exists := gec.activeNodes[nodeID]; exists {
             execution.Status = StatusCompleted
             delete(gec.activeNodes, nodeID)
             gec.currentActive--
         }
         
         // Store result
         gec.mu.Lock()
         gec.completedNodes[nodeID] = result
         gec.mu.Unlock()
         
         // Signal that a slot is available
         gec.condition.Signal()
     }
     ```

5. **Add race detection testing to LangGraph test suite**
   - Create comprehensive race detection tests:

     ```go
     func TestConcurrentStateAccess(t *testing.T) {
         store := NewThreadSafeStateStore()
         
         const numGoroutines = 100
         const numOperations = 1000
         
         var wg sync.WaitGroup
         
         // Concurrent writers
         for i := 0; i < numGoroutines; i++ {
             wg.Add(1)
             go func(id int) {
                 defer wg.Done()
                 
                 for j := 0; j < numOperations; j++ {
                     key := fmt.Sprintf("key_%d_%d", id, j)
                     value := fmt.Sprintf("value_%d_%d", id, j)
                     store.Set(key, value)
                 }
             }(i)
         }
         
         // Concurrent readers
         for i := 0; i < numGoroutines; i++ {
             wg.Add(1)
             go func(id int) {
                 defer wg.Done()
                 
                 for j := 0; j < numOperations; j++ {
                     key := fmt.Sprintf("key_%d_%d", id, j)
                     _, _ = store.Get(key)
                 }
             }(i)
         }
         
         wg.Wait()
         
         // Verify no data corruption
         allData := store.GetAll()
         if len(allData) > numGoroutines*numOperations {
             t.Errorf("Data corruption detected: expected max %d items, got %d", 
                 numGoroutines*numOperations, len(allData))
         }
     }
     
     func TestAtomicStatistics(t *testing.T) {
         stats := &GraphStatistics{}
         
         const numGoroutines = 50
         const numIncrements = 1000
         
         var wg sync.WaitGroup
         
         // Concurrent increments
         for i := 0; i < numGoroutines; i++ {
             wg.Add(1)
             go func() {
                 defer wg.Done()
                 
                 for j := 0; j < numIncrements; j++ {
                     stats.IncrementNodesExecuted()
                     stats.IncrementTotalExecutions()
                     stats.AddDuration(time.Millisecond)
                 }
             }()
         }
         
         wg.Wait()
         
         expected := int64(numGoroutines * numIncrements)
         if stats.GetStats()["nodes_executed"] != expected {
             t.Errorf("Expected %d nodes executed, got %d", 
                 expected, stats.GetStats()["nodes_executed"])
         }
     }
     
     // Benchmark for race detection
     func BenchmarkConcurrentAccess(b *testing.B) {
         store := NewThreadSafeStateStore()
         
         b.RunParallel(func(pb *testing.PB) {
             i := 0
             for pb.Next() {
                 key := fmt.Sprintf("key_%d", i)
                 value := fmt.Sprintf("value_%d", i)
                 
                 store.Set(key, value)
                 _, _ = store.Get(key)
                 
                 i++
             }
         })
     }
     ```

## Key Concepts

- **Data races**: Concurrent access to shared memory without synchronization
- **Atomics**: Operations that complete without interruption
- **Mutexes**: Mutual exclusion locks for protecting critical sections
- **Thread safety**: Code that works correctly with concurrent access
- **Memory consistency**: Ordering guarantees for memory operations

## Code Examples

### Atomic Operations

```go
import "sync/atomic"

type Counter struct {
    value int64
}

func (c *Counter) Increment() {
    atomic.AddInt64(&c.value, 1)
}

func (c *Counter) Get() int64 {
    return atomic.LoadInt64(&c.value)
}

func (c *Counter) CompareAndSwap(old, new int64) bool {
    return atomic.CompareAndSwapInt64(&c.value, old, new)
}
```

### Mutex Protection

```go
type SafeMap struct {
    data map[string]int
    mu   sync.RWMutex
}

func (sm *SafeMap) Set(key string, value int) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    sm.data[key] = value
}

func (sm *SafeMap) Get(key string) (int, bool) {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    value, exists := sm.data[key]
    return value, exists
}
```

### Race Detection

```go
// Run with: go test -race
func TestRaceCondition(t *testing.T) {
    counter := 0
    var wg sync.WaitGroup
    
    for i := 0; i < 1000; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            counter++ // This will trigger race detector
        }()
    }
    
    wg.Wait()
    t.Logf("Counter: %d", counter)
}
```

## Resources

- Ultimate Go Programming 9.1-9.6 transcripts
- [Go Memory Model](https://golang.org/ref/mem)
- [Race Detector](https://golang.org/doc/articles/race_detector.html)
- [Sync Package](https://golang.org/pkg/sync/)

## Validation Checklist

- [ ] Synchronization examples from transcripts implemented
- [ ] Thread-safe state management added to LangGraph
- [ ] Atomic operations implemented for graph statistics
- [ ] Mutex-protected critical sections created
- [ ] Race detection testing added to test suite
- [ ] All tests pass with `-race` flag enabled
