# Module 11: Embedding & Composition

## Video Coverage

**4.3-4.4 Embedding (7:30), Exporting (8:29)**

## Learning Objectives

- Learn Go's composition model and embedding
- Master type embedding and promotion
- Implement composite LangGraph components

## Hands-on Tasks

1. **Implement embedding examples from video transcripts**
   - Study struct embedding syntax and behavior
   - Practice method promotion through embedding
   - Understand field promotion and name conflicts
   - Implement examples showing embedding vs composition

2. **Create composite node types using embedding**
   - Design base node type for common functionality:

     ```go
     type BaseNode struct {
         id       string
         nodeType NodeType
         metadata map[string]interface{}
         created  time.Time
     }
     
     func (bn *BaseNode) ID() string {
         return bn.id
     }
     
     func (bn *BaseNode) Type() NodeType {
         return bn.nodeType
     }
     
     func (bn *BaseNode) SetMetadata(key string, value interface{}) {
         if bn.metadata == nil {
             bn.metadata = make(map[string]interface{})
         }
         bn.metadata[key] = value
     }
     
     func (bn *BaseNode) GetMetadata(key string) (interface{}, bool) {
         if bn.metadata == nil {
             return nil, false
         }
         value, exists := bn.metadata[key]
         return value, exists
     }
     ```

3. **Implement base functionality through embedded types**
   - Create specialized node types with embedded base:

     ```go
     type ProcessingNode struct {
         BaseNode
         function    func(interface{}) (interface{}, error)
         timeout     time.Duration
         retryCount  int
     }
     
     func NewProcessingNode(id string, fn func(interface{}) (interface{}, error)) *ProcessingNode {
         return &ProcessingNode{
             BaseNode: BaseNode{
                 id:       id,
                 nodeType: NodeTypeProcess,
                 created:  time.Now(),
             },
             function:   fn,
             timeout:    30 * time.Second,
             retryCount: 3,
         }
     }
     
     func (pn *ProcessingNode) Execute(ctx context.Context, input interface{}) (interface{}, error) {
         // ProcessingNode-specific execution logic
         if pn.function == nil {
             return nil, fmt.Errorf("no function defined for node %s", pn.ID())
         }
         
         // Use embedded BaseNode methods
         pn.SetMetadata("last_executed", time.Now())
         
         return pn.function(input)
     }
     
     type ConditionalNode struct {
         BaseNode
         condition func(interface{}) bool
         trueNode  string
         falseNode string
     }
     
     func (cn *ConditionalNode) Evaluate(input interface{}) string {
         if cn.condition(input) {
             return cn.trueNode
         }
         return cn.falseNode
     }
     ```

4. **Add proper exporting and visibility controls**
   - Design package structure with proper visibility:

     ```go
     // pkg/nodes/base.go - exported base functionality
     package nodes
     
     type Node interface {
         ID() string
         Type() NodeType
         Execute(ctx context.Context, input interface{}) (interface{}, error)
     }
     
     type BaseNode struct {
         id       string
         nodeType NodeType
         metadata map[string]interface{}
     }
     
     // Exported methods
     func (bn *BaseNode) ID() string { return bn.id }
     func (bn *BaseNode) Type() NodeType { return bn.nodeType }
     
     // pkg/nodes/processing.go - specialized implementations
     type ProcessingNode struct {
         BaseNode
         function func(interface{}) (interface{}, error) // unexported
     }
     
     // Exported constructor
     func NewProcessingNode(id string, fn func(interface{}) (interface{}, error)) *ProcessingNode {
         return &ProcessingNode{
             BaseNode: BaseNode{id: id, nodeType: NodeTypeProcess},
             function: fn,
         }
     }
     ```

5. **Design extensible LangGraph component architecture**
   - Create plugin-friendly architecture using embedding:

     ```go
     type Plugin interface {
         Name() string
         Execute(ctx context.Context, input interface{}) (interface{}, error)
     }
     
     type BasePlugin struct {
         name    string
         version string
         config  map[string]interface{}
     }
     
     func (bp *BasePlugin) Name() string {
         return bp.name
     }
     
     func (bp *BasePlugin) Version() string {
         return bp.version
     }
     
     type TransformPlugin struct {
         BasePlugin
         transformer func(interface{}) interface{}
     }
     
     func (tp *TransformPlugin) Execute(ctx context.Context, input interface{}) (interface{}, error) {
         if tp.transformer == nil {
             return input, nil
         }
         return tp.transformer(input), nil
     }
     ```

## Key Concepts

- **Embedding**: Including one struct type inside another
- **Composition**: Building complex types from simpler ones
- **Type promotion**: Automatic forwarding of embedded type methods
- **Visibility**: Controlling access through exported/unexported identifiers

## Code Examples

### Basic Embedding

```go
type Person struct {
    Name string
    Age  int
}

func (p *Person) Greet() string {
    return fmt.Sprintf("Hello, I'm %s", p.Name)
}

type Employee struct {
    Person    // embedded type
    JobTitle  string
    Salary    float64
}

func (e *Employee) Work() string {
    return fmt.Sprintf("%s is working as %s", e.Name, e.JobTitle)
}

// Usage
emp := Employee{
    Person:   Person{Name: "Alice", Age: 30},
    JobTitle: "Developer",
    Salary:   75000,
}

fmt.Println(emp.Greet()) // Method promoted from Person
fmt.Println(emp.Work())  // Employee's own method
```

### Method Promotion and Overriding

```go
type Logger struct {
    prefix string
}

func (l *Logger) Log(message string) {
    fmt.Printf("[%s] %s\n", l.prefix, message)
}

type TimestampLogger struct {
    Logger
}

// Override embedded method
func (tl *TimestampLogger) Log(message string) {
    timestamp := time.Now().Format("2006-01-02 15:04:05")
    tl.Logger.Log(fmt.Sprintf("%s - %s", timestamp, message))
}

type FileLogger struct {
    Logger
    filename string
}

func (fl *FileLogger) LogToFile(message string) error {
    file, err := os.OpenFile(fl.filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
    if err != nil {
        return err
    }
    defer file.Close()
    
    _, err = file.WriteString(fmt.Sprintf("[%s] %s\n", fl.prefix, message))
    return err
}
```

### Interface Satisfaction Through Embedding

```go
type Validator interface {
    Validate() error
}

type Serializer interface {
    Serialize() ([]byte, error)
}

type BaseModel struct {
    ID        string
    CreatedAt time.Time
}

func (bm *BaseModel) Validate() error {
    if bm.ID == "" {
        return errors.New("ID cannot be empty")
    }
    return nil
}

func (bm *BaseModel) Serialize() ([]byte, error) {
    return json.Marshal(bm)
}

type User struct {
    BaseModel
    Name  string
    Email string
}

// User automatically satisfies Validator and Serializer interfaces
// through embedding BaseModel

var _ Validator = (*User)(nil)
var _ Serializer = (*User)(nil)
```

## Resources

- Ultimate Go Programming 4.3-4.4 Embedding and Exporting transcripts
- [Embedding in Go](https://golang.org/doc/effective_go#embedding)
- [Go by Example - Struct Embedding](https://gobyexample.com/struct-embedding)
- [Composition vs Inheritance](https://golang.org/doc/faq#inheritance)

## Validation Checklist

- [ ] Embedding examples from transcripts implemented and understood
- [ ] Composite node types created using embedding
- [ ] Base functionality implemented through embedded types
- [ ] Proper visibility controls applied to package design
- [ ] Extensible component architecture designed with embedding
- [ ] Method promotion and overriding patterns mastered
