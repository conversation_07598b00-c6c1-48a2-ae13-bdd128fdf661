# Module 10: Interfaces & Polymorphism

## Video Coverage

**4.2 Interfaces Parts 1-3 (20:11, 11:51, 5:34)**

## Learning Objectives

- Master interface mechanics and polymorphism
- Understand method sets and interface compliance
- Design core LangGraph interfaces

## Hands-on Tasks

1. **Analyze interface examples from video transcripts**
   - Study the reader interface example from the transcript
   - Understand interface types vs concrete types
   - Practice interface declaration and implementation
   - <PERSON>rn about implicit interface satisfaction

2. **Design and implement core LangGraph interfaces (Node, Channel, Runnable)**
   - Create fundamental LangGraph interfaces:

     ```go
     // Core execution interface
     type Runnable interface {
         Run(ctx context.Context, input interface{}) (interface{}, error)
     }
     
     // Node interface for graph components
     type NodeInterface interface {
         ID() string
         Type() NodeType
         Execute(ctx context.Context, state interface{}) (interface{}, error)
         Validate() error
     }
     
     // Channel interface for communication
     type Channel interface {
         Send(ctx context.Context, value interface{}) error
         Receive(ctx context.Context) (interface{}, error)
         Close() error
     }
     
     // State management interface
     type StateManager interface {
         Get(key string) (interface{}, bool)
         Set(key string, value interface{}) error
         Delete(key string) error
         Keys() []string
     }
     ```

3. **Create polymorphic execution patterns for different node types**
   - Implement different node types that satisfy the same interface:

     ```go
     type InputNode struct {
         id   string
         data interface{}
     }
     
     func (n *InputNode) ID() string { return n.id }
     func (n *InputNode) Type() NodeType { return NodeTypeInput }
     func (n *InputNode) Execute(ctx context.Context, state interface{}) (interface{}, error) {
         return n.data, nil
     }
     func (n *InputNode) Validate() error { return nil }
     
     type ProcessingNode struct {
         id       string
         function func(interface{}) (interface{}, error)
     }
     
     func (n *ProcessingNode) ID() string { return n.id }
     func (n *ProcessingNode) Type() NodeType { return NodeTypeProcess }
     func (n *ProcessingNode) Execute(ctx context.Context, state interface{}) (interface{}, error) {
         return n.function(state)
     }
     func (n *ProcessingNode) Validate() error {
         if n.function == nil {
             return fmt.Errorf("processing node %s has no function", n.id)
         }
         return nil
     }
     ```

4. **Implement interface-based plugin system for LangGraph**
   - Create extensible plugin architecture:

     ```go
     type Plugin interface {
         Name() string
         Version() string
         Initialize(config map[string]interface{}) error
         Execute(ctx context.Context, input interface{}) (interface{}, error)
         Cleanup() error
     }
     
     type PluginRegistry struct {
         plugins map[string]Plugin
         mu      sync.RWMutex
     }
     
     func (pr *PluginRegistry) Register(plugin Plugin) error {
         pr.mu.Lock()
         defer pr.mu.Unlock()
         
         if err := plugin.Initialize(nil); err != nil {
             return fmt.Errorf("failed to initialize plugin %s: %w", plugin.Name(), err)
         }
         
         pr.plugins[plugin.Name()] = plugin
         return nil
     }
     
     func (pr *PluginRegistry) Execute(name string, ctx context.Context, input interface{}) (interface{}, error) {
         pr.mu.RLock()
         plugin, exists := pr.plugins[name]
         pr.mu.RUnlock()
         
         if !exists {
             return nil, fmt.Errorf("plugin %s not found", name)
         }
         
         return plugin.Execute(ctx, input)
     }
     ```

5. **Add interface documentation and usage examples**
   - Document interface contracts and expected behavior
   - Create comprehensive usage examples
   - Add interface design guidelines
   - Implement example implementations for each interface

## Key Concepts

- **Interfaces**: Contracts that define method signatures
- **Polymorphism**: Single interface, multiple implementations
- **Method sets**: Collection of methods that satisfy an interface
- **Decoupling**: Separating interface from implementation

## Code Examples

### Interface Declaration and Implementation

```go
// Interface definition
type Writer interface {
    Write([]byte) (int, error)
}

// Multiple implementations
type FileWriter struct {
    filename string
}

func (fw *FileWriter) Write(data []byte) (int, error) {
    return len(data), ioutil.WriteFile(fw.filename, data, 0644)
}

type BufferWriter struct {
    buffer []byte
}

func (bw *BufferWriter) Write(data []byte) (int, error) {
    bw.buffer = append(bw.buffer, data...)
    return len(data), nil
}

// Polymorphic usage
func WriteData(w Writer, data []byte) error {
    _, err := w.Write(data)
    return err
}
```

### Interface Composition

```go
type Reader interface {
    Read([]byte) (int, error)
}

type Writer interface {
    Write([]byte) (int, error)
}

type Closer interface {
    Close() error
}

// Composed interface
type ReadWriteCloser interface {
    Reader
    Writer
    Closer
}

// Implementation
type File struct {
    name string
    data []byte
    pos  int
}

func (f *File) Read(p []byte) (int, error) {
    // Implementation
    return 0, nil
}

func (f *File) Write(p []byte) (int, error) {
    // Implementation
    return len(p), nil
}

func (f *File) Close() error {
    // Implementation
    return nil
}

// File automatically satisfies ReadWriteCloser
var _ ReadWriteCloser = (*File)(nil)
```

### Type Assertion and Interface Checking

```go
func ProcessNode(node NodeInterface) error {
    // Type assertion to access specific methods
    if processingNode, ok := node.(*ProcessingNode); ok {
        // Access ProcessingNode-specific methods
        return processingNode.ValidateFunction()
    }
    
    // Interface assertion to check for optional interfaces
    if validator, ok := node.(interface{ Validate() error }); ok {
        return validator.Validate()
    }
    
    return nil
}

// Interface checking at compile time
var (
    _ NodeInterface = (*InputNode)(nil)
    _ NodeInterface = (*ProcessingNode)(nil)
    _ Runnable      = (*ProcessingNode)(nil)
)
```

## Resources

- Ultimate Go Programming 4.2 Interfaces Parts 1-3 transcripts
- [Go Interfaces](https://tour.golang.org/methods/9)
- [Interface Values](https://tour.golang.org/methods/11)
- [Effective Go - Interfaces](https://golang.org/doc/effective_go#interfaces)

## Validation Checklist

- [ ] Interface examples from transcripts analyzed and understood
- [ ] Core LangGraph interfaces designed and implemented
- [ ] Polymorphic execution patterns created for different node types
- [ ] Interface-based plugin system implemented
- [ ] Comprehensive interface documentation and examples written
- [ ] Interface compliance verified with compile-time checks
