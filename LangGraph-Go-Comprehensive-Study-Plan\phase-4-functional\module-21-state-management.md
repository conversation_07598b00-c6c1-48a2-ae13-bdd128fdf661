# Module 21: State Management with Functional Patterns

## Learning Objectives

- Implement Reader and State monads for LangGraph's context and state management
- Design functional state transitions and immutable data structures
- Create composable state management patterns

## Hands-on Tasks

1. **Implement Reader monad for dependency injection in LangGraph**
2. **Create State monad for stateful graph computations**
3. **Design immutable state transition functions**
4. **Add functional state management to Pregel algorithm**
5. **Implement state composition and transformation utilities**

## Key Concepts

- **Reader**: Monad for dependency injection and context passing
- **State**: Monad for stateful computations
- **Dependency injection**: Providing dependencies through context
- **Immutable state**: State that cannot be modified after creation
- **Composition**: Combining state operations

## Code Examples

### Reader Monad for Dependency Injection

```go
import "github.com/IBM/fp-go/reader"

type GraphContext struct {
    Logger    Logger
    Config    Config
    Metrics   MetricsCollector
    Storage   StateStore
}

// Reader-based operations
func LogExecution(message string) reader.Reader[GraphContext, unit.Unit] {
    return reader.Ask[GraphContext]().Map(func(ctx GraphContext) unit.Unit {
        ctx.Logger.Info(message)
        return unit.Unit{}
    })
}

func GetConfig() reader.Reader[GraphContext, Config] {
    return reader.Ask[GraphContext]().Map(func(ctx GraphContext) Config {
        return ctx.Config
    })
}

// Compose operations
func ExecuteWithLogging(node *Node, input interface{}) reader.Reader[GraphContext, either.Either[GraphError, interface{}]] {
    return reader.Chain(
        LogExecution(fmt.Sprintf("Executing node %s", node.ID())),
        func(_ unit.Unit) reader.Reader[GraphContext, either.Either[GraphError, interface{}]] {
            return reader.Of[GraphContext](ExecuteNodeSafely(node, input))
        },
    )
}
```

### State Monad for Stateful Computations

```go
import "github.com/IBM/fp-go/state"

type GraphState struct {
    NodeStates    map[string]interface{}
    ExecutionLog  []string
    ErrorCount    int
    LastExecution time.Time
}

// State operations
func UpdateNodeState(nodeID string, newState interface{}) state.State[GraphState, unit.Unit] {
    return state.Modify[GraphState](func(s GraphState) GraphState {
        newStates := make(map[string]interface{})
        for k, v := range s.NodeStates {
            newStates[k] = v
        }
        newStates[nodeID] = newState
        
        return GraphState{
            NodeStates:    newStates,
            ExecutionLog:  append(s.ExecutionLog, fmt.Sprintf("Updated node %s", nodeID)),
            ErrorCount:    s.ErrorCount,
            LastExecution: time.Now(),
        }
    })
}

func IncrementErrorCount() state.State[GraphState, unit.Unit] {
    return state.Modify[GraphState](func(s GraphState) GraphState {
        return GraphState{
            NodeStates:    s.NodeStates,
            ExecutionLog:  s.ExecutionLog,
            ErrorCount:    s.ErrorCount + 1,
            LastExecution: s.LastExecution,
        }
    })
}

func GetNodeState(nodeID string) state.State[GraphState, option.Option[interface{}]] {
    return state.Get[GraphState]().Map(func(s GraphState) option.Option[interface{}] {
        if value, exists := s.NodeStates[nodeID]; exists {
            return option.Some(value)
        }
        return option.None[interface{}]()
    })
}
```

## Resources

- [IBM/fp-go State Documentation](https://github.com/IBM/fp-go)
- [Reader Monad Pattern](https://hackage.haskell.org/package/mtl/docs/Control-Monad-Reader.html)

## Validation Checklist

- [ ] Reader monad implemented for dependency injection
- [ ] State monad created for stateful computations
- [ ] Immutable state transition functions designed
- [ ] Functional state management added to Pregel algorithm
- [ ] State composition utilities implemented
