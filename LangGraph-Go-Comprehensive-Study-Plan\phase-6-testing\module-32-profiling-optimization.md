# Module 32: Profiling & Performance Optimization

## Video Coverage

**14.1-14.4 Profiling Guidelines (10:48), Stack Traces (9:00), Micro Level Optimization (31:17), Macro Level Optimization (12:49)**

## Learning Objectives

- Learn profiling techniques and optimization strategies
- Master micro and macro level optimization approaches
- Optimize LangGraph's performance using Go profiling tools

## Hands-on Tasks

1. **Implement profiling examples from video transcripts**
2. **Profile LangGraph execution and identify hotspots**
3. **Apply micro-level optimizations to critical paths**
4. **Implement macro-level architectural optimizations**
5. **Create performance monitoring and alerting**

## Key Concepts

- **Profiling**: Analyzing program performance characteristics
- **Optimization**: Improving performance based on profiling data
- **Performance tuning**: Systematic approach to performance improvement
- **Monitoring**: Continuous performance observation

## Code Examples

### CPU Profiling

```go
func ProfileLangGraphExecution() {
    // Start CPU profiling
    f, err := os.Create("cpu.prof")
    if err != nil {
        log.Fatal(err)
    }
    defer f.Close()
    
    if err := pprof.StartCPUProfile(f); err != nil {
        log.Fatal(err)
    }
    defer pprof.StopCPUProfile()
    
    // Run the code to be profiled
    graph := CreateLargeGraph(1000)
    for i := 0; i < 100; i++ {
        graph.Execute(context.Background())
    }
}

// Optimized node execution
func (n *Node) ExecuteOptimized(ctx context.Context, input interface{}) (interface{}, error) {
    // Fast path for nil function
    if n.function == nil {
        return nil, ErrNoFunction
    }
    
    // Pre-allocate result channel to avoid allocation in hot path
    if n.resultChan == nil {
        n.resultChan = make(chan executionResult, 1)
    }
    
    // Use object pool for frequent allocations
    result := n.resultPool.Get().(*executionResult)
    defer n.resultPool.Put(result)
    
    // Execute with timeout
    done := make(chan struct{})
    go func() {
        defer close(done)
        result.output, result.err = n.function(ctx, input)
    }()
    
    select {
    case <-done:
        return result.output, result.err
    case <-ctx.Done():
        return nil, ctx.Err()
    }
}
```

### Memory Profiling

```go
func ProfileMemoryUsage() {
    // Create memory profile
    f, err := os.Create("mem.prof")
    if err != nil {
        log.Fatal(err)
    }
    defer f.Close()
    
    // Run memory-intensive operations
    graph := CreateMemoryIntensiveGraph()
    graph.Execute(context.Background())
    
    // Force GC and write memory profile
    runtime.GC()
    if err := pprof.WriteHeapProfile(f); err != nil {
        log.Fatal(err)
    }
}

// Memory-optimized graph structure
type OptimizedGraph struct {
    nodes    []Node          // Use slice instead of map for better cache locality
    nodeMap  map[string]int  // Index mapping for O(1) lookup
    edges    []Edge          // Pre-allocated edge slice
    pool     sync.Pool       // Object pool for temporary allocations
}

func (g *OptimizedGraph) Execute(ctx context.Context) (*ExecutionResult, error) {
    // Use object pool for result
    result := g.pool.Get().(*ExecutionResult)
    defer g.pool.Put(result)
    
    // Reset result for reuse
    result.Reset()
    
    // Pre-allocate output map with known capacity
    result.Outputs = make(map[string]interface{}, len(g.nodes))
    
    // Execute nodes with minimal allocations
    for i := range g.nodes {
        output, err := g.nodes[i].Execute(ctx, nil)
        if err != nil {
            return nil, err
        }
        result.Outputs[g.nodes[i].ID()] = output
    }
    
    return result, nil
}
```

### Micro-Level Optimizations

```go
// Before: Inefficient string concatenation
func BuildNodeID(prefix string, index int) string {
    return prefix + "-" + strconv.Itoa(index)
}

// After: Efficient string building
func BuildNodeIDOptimized(prefix string, index int) string {
    var builder strings.Builder
    builder.Grow(len(prefix) + 10) // Pre-allocate capacity
    builder.WriteString(prefix)
    builder.WriteByte('-')
    builder.WriteString(strconv.Itoa(index))
    return builder.String()
}

// Before: Inefficient slice operations
func FilterNodes(nodes []Node, predicate func(Node) bool) []Node {
    var result []Node
    for _, node := range nodes {
        if predicate(node) {
            result = append(result, node)
        }
    }
    return result
}

// After: Pre-allocated slice with capacity
func FilterNodesOptimized(nodes []Node, predicate func(Node) bool) []Node {
    result := make([]Node, 0, len(nodes)) // Pre-allocate with capacity
    for _, node := range nodes {
        if predicate(node) {
            result = append(result, node)
        }
    }
    return result
}
```

### Macro-Level Optimizations

```go
// Architectural optimization: Batch processing
type BatchProcessor struct {
    batchSize int
    timeout   time.Duration
    buffer    []Job
    mu        sync.Mutex
}

func (bp *BatchProcessor) ProcessBatch(jobs []Job) error {
    // Process multiple jobs together to amortize overhead
    ctx, cancel := context.WithTimeout(context.Background(), bp.timeout)
    defer cancel()
    
    // Use worker pool for parallel processing
    workers := runtime.NumCPU()
    jobChan := make(chan Job, len(jobs))
    resultChan := make(chan JobResult, len(jobs))
    
    // Start workers
    var wg sync.WaitGroup
    for i := 0; i < workers; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for job := range jobChan {
                result := bp.processJob(ctx, job)
                resultChan <- result
            }
        }()
    }
    
    // Send jobs
    for _, job := range jobs {
        jobChan <- job
    }
    close(jobChan)
    
    // Wait for completion
    wg.Wait()
    close(resultChan)
    
    // Collect results
    for result := range resultChan {
        if result.Error != nil {
            return result.Error
        }
    }
    
    return nil
}
```

## Resources

- Ultimate Go Programming 14.1-14.4 transcripts
- [Go Profiling](https://golang.org/doc/diagnostics.html#profiling)
- [pprof Tool](https://golang.org/pkg/net/http/pprof/)

## Validation Checklist

- [ ] Profiling examples from transcripts implemented
- [ ] LangGraph execution profiled and hotspots identified
- [ ] Micro-level optimizations applied to critical paths
- [ ] Macro-level architectural optimizations implemented
- [ ] Performance monitoring and alerting created
