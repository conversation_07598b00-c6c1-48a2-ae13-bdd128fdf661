# Module 23: Goroutines & Scheduler Mechanics

## Video Coverage

**8.1-8.3 OS Scheduler Mechanics (28:59), Go Scheduler Mechanics (20:41), Creating Goroutines (19:43)**

## Learning Objectives

- Understand Go's concurrency model and scheduler
- Master goroutine creation and lifecycle management
- Design LangGraph's concurrent execution model

## Hands-on Tasks

1. **Implement goroutine examples from video transcripts**
   - Study OS scheduler mechanics and Go's M:N threading model
   - Practice goroutine creation and basic concurrency patterns
   - Understand the Go scheduler's work-stealing algorithm
   - Implement examples showing goroutine lifecycle and scheduling

2. **Design concurrent node execution patterns for LangGraph**
   - Create concurrent node execution framework:

     ```go
     type ConcurrentExecutor struct {
         maxWorkers   int
         workerPool   chan struct{}
         resultChan   chan *NodeResult
         errorChan    chan error
         ctx          context.Context
         cancel       context.CancelFunc
         wg           sync.WaitGroup
     }
     
     type NodeResult struct {
         NodeID string
         Output interface{}
         Error  error
         Duration time.Duration
     }
     
     func NewConcurrentExecutor(maxWorkers int) *ConcurrentExecutor {
         ctx, cancel := context.WithCancel(context.Background())
         return &ConcurrentExecutor{
             maxWorkers: maxWorkers,
             workerPool: make(chan struct{}, maxWorkers),
             resultChan: make(chan *NodeResult, maxWorkers*2),
             errorChan:  make(chan error, maxWorkers),
             ctx:        ctx,
             cancel:     cancel,
         }
     }
     
     func (ce *ConcurrentExecutor) ExecuteNode(node *Node, input interface{}) {
         ce.wg.Add(1)
         
         go func() {
             defer ce.wg.Done()
             
             // Acquire worker slot
             ce.workerPool <- struct{}{}
             defer func() { <-ce.workerPool }()
             
             start := time.Now()
             output, err := node.Execute(ce.ctx, input)
             duration := time.Since(start)
             
             result := &NodeResult{
                 NodeID:   node.ID(),
                 Output:   output,
                 Error:    err,
                 Duration: duration,
             }
             
             select {
             case ce.resultChan <- result:
             case <-ce.ctx.Done():
                 return
             }
         }()
     }
     ```

3. **Create goroutine pools for graph processing**
   - Implement worker pool pattern for efficient resource usage:

     ```go
     type WorkerPool struct {
         workers    int
         jobQueue   chan Job
         resultChan chan Result
         quit       chan bool
         wg         sync.WaitGroup
     }
     
     type Job struct {
         ID       string
         Node     *Node
         Input    interface{}
         Context  context.Context
     }
     
     type Result struct {
         JobID    string
         Output   interface{}
         Error    error
         WorkerID int
     }
     
     func NewWorkerPool(workers int, queueSize int) *WorkerPool {
         return &WorkerPool{
             workers:    workers,
             jobQueue:   make(chan Job, queueSize),
             resultChan: make(chan Result, queueSize),
             quit:       make(chan bool),
         }
     }
     
     func (wp *WorkerPool) Start() {
         for i := 0; i < wp.workers; i++ {
             wp.wg.Add(1)
             go wp.worker(i)
         }
     }
     
     func (wp *WorkerPool) worker(id int) {
         defer wp.wg.Done()
         
         for {
             select {
             case job := <-wp.jobQueue:
                 output, err := job.Node.Execute(job.Context, job.Input)
                 
                 result := Result{
                     JobID:    job.ID,
                     Output:   output,
                     Error:    err,
                     WorkerID: id,
                 }
                 
                 select {
                 case wp.resultChan <- result:
                 case <-wp.quit:
                     return
                 }
                 
             case <-wp.quit:
                 return
             }
         }
     }
     
     func (wp *WorkerPool) Submit(job Job) {
         wp.jobQueue <- job
     }
     
     func (wp *WorkerPool) Results() <-chan Result {
         return wp.resultChan
     }
     
     func (wp *WorkerPool) Stop() {
         close(wp.quit)
         wp.wg.Wait()
         close(wp.resultChan)
     }
     ```

4. **Implement scheduler-aware execution strategies**
   - Create execution strategies that work with Go's scheduler:

     ```go
     type SchedulerAwareExecutor struct {
         strategy ExecutionStrategy
         metrics  *SchedulerMetrics
     }
     
     type SchedulerMetrics struct {
         goroutineCount    int64
         schedulerLatency  time.Duration
         contextSwitches   int64
         mu                sync.RWMutex
     }
     
     func (sae *SchedulerAwareExecutor) Execute(graph *Graph) (*ExecutionResult, error) {
         // Monitor scheduler performance
         sae.metrics.mu.Lock()
         initialGoroutines := runtime.NumGoroutine()
         sae.metrics.goroutineCount = int64(initialGoroutines)
         sae.metrics.mu.Unlock()
         
         // Adaptive concurrency based on system load
         maxConcurrency := sae.calculateOptimalConcurrency()
         
         // Execute with scheduler awareness
         result, err := sae.executeWithSchedulerMonitoring(graph, maxConcurrency)
         
         // Update metrics
         sae.updateSchedulerMetrics()
         
         return result, err
     }
     
     func (sae *SchedulerAwareExecutor) calculateOptimalConcurrency() int {
         numCPU := runtime.NumCPU()
         currentGoroutines := runtime.NumGoroutine()
         
         // Adaptive algorithm based on current load
         if currentGoroutines > numCPU*10 {
             return numCPU // Conservative approach
         }
         
         return numCPU * 2 // Aggressive approach
     }
     ```

5. **Add goroutine lifecycle management to LangGraph runtime**
   - Implement comprehensive goroutine lifecycle management:

     ```go
     type GoroutineManager struct {
         active    map[string]*GoroutineInfo
         mu        sync.RWMutex
         maxLife   time.Duration
         cleanup   time.Duration
         ticker    *time.Ticker
         done      chan struct{}
     }
     
     type GoroutineInfo struct {
         ID        string
         StartTime time.Time
         LastSeen  time.Time
         Cancel    context.CancelFunc
         Status    GoroutineStatus
     }
     
     type GoroutineStatus int
     
     const (
         StatusRunning GoroutineStatus = iota
         StatusCompleted
         StatusCancelled
         StatusTimedOut
     )
     
     func NewGoroutineManager(maxLife, cleanup time.Duration) *GoroutineManager {
         gm := &GoroutineManager{
             active:  make(map[string]*GoroutineInfo),
             maxLife: maxLife,
             cleanup: cleanup,
             ticker:  time.NewTicker(cleanup),
             done:    make(chan struct{}),
         }
         
         go gm.cleanupLoop()
         return gm
     }
     
     func (gm *GoroutineManager) StartGoroutine(id string, fn func(context.Context)) {
         ctx, cancel := context.WithTimeout(context.Background(), gm.maxLife)
         
         info := &GoroutineInfo{
             ID:        id,
             StartTime: time.Now(),
             LastSeen:  time.Now(),
             Cancel:    cancel,
             Status:    StatusRunning,
         }
         
         gm.mu.Lock()
         gm.active[id] = info
         gm.mu.Unlock()
         
         go func() {
             defer func() {
                 gm.mu.Lock()
                 if info, exists := gm.active[id]; exists {
                     info.Status = StatusCompleted
                 }
                 gm.mu.Unlock()
                 cancel()
             }()
             
             fn(ctx)
         }()
     }
     
     func (gm *GoroutineManager) cleanupLoop() {
         for {
             select {
             case <-gm.ticker.C:
                 gm.cleanupExpired()
             case <-gm.done:
                 return
             }
         }
     }
     
     func (gm *GoroutineManager) cleanupExpired() {
         now := time.Now()
         
         gm.mu.Lock()
         defer gm.mu.Unlock()
         
         for id, info := range gm.active {
             if now.Sub(info.StartTime) > gm.maxLife {
                 info.Cancel()
                 info.Status = StatusTimedOut
                 delete(gm.active, id)
             }
         }
     }
     ```

## Key Concepts

- **Goroutines**: Lightweight threads managed by Go runtime
- **Scheduler**: Go's M:N threading model and work-stealing scheduler
- **Concurrency**: Executing multiple tasks simultaneously
- **Parallel execution**: True simultaneous execution on multiple cores

## Code Examples

### Basic Goroutine Creation

```go
func main() {
    // Simple goroutine
    go func() {
        fmt.Println("Hello from goroutine")
    }()
    
    // Goroutine with parameters
    message := "Hello World"
    go func(msg string) {
        fmt.Println(msg)
    }(message)
    
    // Wait for goroutines to complete
    time.Sleep(time.Second)
}
```

### Goroutine with WaitGroup

```go
func processNodes(nodes []*Node) {
    var wg sync.WaitGroup
    
    for _, node := range nodes {
        wg.Add(1)
        go func(n *Node) {
            defer wg.Done()
            
            result, err := n.Execute(context.Background(), nil)
            if err != nil {
                log.Printf("Node %s failed: %v", n.ID(), err)
                return
            }
            
            log.Printf("Node %s completed: %v", n.ID(), result)
        }(node)
    }
    
    wg.Wait()
    fmt.Println("All nodes completed")
}
```

### Scheduler Monitoring

```go
func monitorScheduler() {
    ticker := time.NewTicker(time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            fmt.Printf("Goroutines: %d, CPUs: %d\n", 
                runtime.NumGoroutine(), 
                runtime.NumCPU())
            
            var m runtime.MemStats
            runtime.ReadMemStats(&m)
            fmt.Printf("Alloc: %d KB, Sys: %d KB\n", 
                m.Alloc/1024, 
                m.Sys/1024)
        }
    }
}
```

## Resources

- Ultimate Go Programming 8.1-8.3 transcripts
- [Go Concurrency Patterns](https://blog.golang.org/concurrency-patterns)
- [Go Scheduler Design](https://docs.google.com/document/d/1TTj4T2JO42uD5ID9e89oa0sLKhJYD0Y_kqxDv3I3XMw)
- [Effective Go - Goroutines](https://golang.org/doc/effective_go#goroutines)

## Validation Checklist

- [ ] Goroutine examples from transcripts implemented and understood
- [ ] Concurrent node execution patterns designed and tested
- [ ] Goroutine pools created for efficient resource management
- [ ] Scheduler-aware execution strategies implemented
- [ ] Goroutine lifecycle management added to LangGraph runtime
- [ ] Performance monitoring and metrics collection implemented
