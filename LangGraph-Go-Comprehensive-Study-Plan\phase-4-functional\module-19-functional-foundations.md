# Module 19: Functional Programming Foundations

## Learning Objectives

- Study IBM/fp-go library structure and core functional concepts
- Analyze Either, Option, IO monads and their applications
- Implement basic functional patterns for LangGraph's data flow

## Hands-on Tasks

1. **Explore fp-go library structure and documentation**
   - Study the IBM/fp-go library organization and core modules
   - Understand functional programming concepts in Go context
   - Analyze the library's approach to monads and functional composition
   - Practice basic functional patterns with simple examples

2. **Implement Option monad for nullable LangGraph values**
   - Create Option type for handling nullable values safely:

     ```go
     package option
     
     import "github.com/IBM/fp-go/option"
     
     // Wrap LangGraph operations with Option
     func GetNode(g *Graph, id string) option.Option[*Node] {
         if node, exists := g.nodes[id]; exists {
             return option.Some(node)
         }
         return option.None[*Node]()
     }
     
     func GetNodeMetadata(node *Node, key string) option.Option[interface{}] {
         if value, exists := node.metadata[key]; exists {
             return option.Some(value)
         }
         return option.None[interface{}]()
     }
     
     // Chain operations safely
     func ProcessNodeSafely(g *Graph, nodeID string, key string) option.Option[string] {
         return option.Chain(
             GetNode(g, nodeID),
             func(node *Node) option.Option[interface{}] {
                 return GetNodeMetadata(node, key)
             },
         ).Chain(func(value interface{}) option.Option[string] {
             if str, ok := value.(string); ok {
                 return option.Some(str)
             }
             return option.None[string]()
         })
     }
     ```

3. **Create Either monad for error handling in graph operations**
   - Implement Either for functional error handling:

     ```go
     package either
     
     import "github.com/IBM/fp-go/either"
     
     type GraphError struct {
         Message string
         Code    int
     }
     
     func (e GraphError) Error() string {
         return e.Message
     }
     
     // Either-based operations
     func ValidateNode(node *Node) either.Either[GraphError, *Node] {
         if node == nil {
             return either.Left[*Node](GraphError{
                 Message: "node cannot be nil",
                 Code:    400,
             })
         }
         
         if node.ID() == "" {
             return either.Left[*Node](GraphError{
                 Message: "node ID cannot be empty",
                 Code:    400,
             })
         }
         
         return either.Right[GraphError](node)
     }
     
     func ExecuteNode(node *Node, input interface{}) either.Either[GraphError, interface{}] {
         return either.Chain(
             ValidateNode(node),
             func(validNode *Node) either.Either[GraphError, interface{}] {
                 output, err := validNode.Execute(context.Background(), input)
                 if err != nil {
                     return either.Left[interface{}](GraphError{
                         Message: err.Error(),
                         Code:    500,
                     })
                 }
                 return either.Right[GraphError](output)
             },
         )
     }
     ```

4. **Add functional composition utilities for node processing**
   - Create composable processing functions:

     ```go
     package compose
     
     import (
         "github.com/IBM/fp-go/function"
         "github.com/IBM/fp-go/either"
     )
     
     type Processor[A, B any] func(A) either.Either[GraphError, B]
     
     // Compose processors
     func Compose[A, B, C any](
         f Processor[A, B],
         g Processor[B, C],
     ) Processor[A, C] {
         return func(a A) either.Either[GraphError, C] {
             return either.Chain(f(a), g)
         }
     }
     
     // Pipeline of processors
     func Pipeline[T any](processors ...Processor[T, T]) Processor[T, T] {
         return func(input T) either.Either[GraphError, T] {
             result := either.Right[GraphError](input)
             
             for _, processor := range processors {
                 result = either.Chain(result, processor)
             }
             
             return result
         }
     }
     
     // Example processors
     func ValidateInput(input interface{}) either.Either[GraphError, interface{}] {
         if input == nil {
             return either.Left[interface{}](GraphError{
                 Message: "input cannot be nil",
                 Code:    400,
             })
         }
         return either.Right[GraphError](input)
     }
     
     func TransformData(data interface{}) either.Either[GraphError, interface{}] {
         // Transform logic here
         return either.Right[GraphError](data)
     }
     
     func SaveResult(result interface{}) either.Either[GraphError, interface{}] {
         // Save logic here
         return either.Right[GraphError](result)
     }
     
     // Usage
     func ProcessData(input interface{}) either.Either[GraphError, interface{}] {
         pipeline := Pipeline(ValidateInput, TransformData, SaveResult)
         return pipeline(input)
     }
     ```

5. **Integrate basic monadic patterns into existing LangGraph code**
   - Refactor existing code to use functional patterns:

     ```go
     // Before: Traditional error handling
     func (g *Graph) ExecuteTraditional(ctx context.Context) (*Result, error) {
         result := &Result{Outputs: make(map[string]interface{})}
         
         for id, node := range g.nodes {
             output, err := node.Execute(ctx, nil)
             if err != nil {
                 return nil, fmt.Errorf("node %s failed: %w", id, err)
             }
             result.Outputs[id] = output
         }
         
         return result, nil
     }
     
     // After: Functional approach
     func (g *Graph) ExecuteFunctional(ctx context.Context) either.Either[GraphError, *Result] {
         result := &Result{Outputs: make(map[string]interface{})}
         
         for id, node := range g.nodes {
             nodeResult := ExecuteNode(node, nil)
             
             switch nodeResult := nodeResult.(type) {
             case either.Left[interface{}]:
                 return either.Left[*Result](nodeResult.Value)
             case either.Right[GraphError]:
                 result.Outputs[id] = nodeResult.Value
             }
         }
         
         return either.Right[GraphError](result)
     }
     ```

## Key Concepts

- **Monads**: Containers that provide a way to wrap values and chain operations
- **Option**: Monad for handling nullable values safely
- **Either**: Monad for error handling and branching logic
- **Functional composition**: Combining simple functions to build complex operations
- **Immutability**: Data structures that cannot be modified after creation

## Code Examples

### Option Monad Usage

```go
import "github.com/IBM/fp-go/option"

// Safe division with Option
func safeDivide(a, b float64) option.Option[float64] {
    if b == 0 {
        return option.None[float64]()
    }
    return option.Some(a / b)
}

// Chain operations
result := option.Chain(
    safeDivide(10, 2),
    func(x float64) option.Option[float64] {
        return safeDivide(x, 2)
    },
)

// Handle result
option.Match(
    result,
    func() { fmt.Println("Division by zero") },
    func(value float64) { fmt.Printf("Result: %f\n", value) },
)
```

### Either Monad Usage

```go
import "github.com/IBM/fp-go/either"

type ValidationError struct {
    Field   string
    Message string
}

func (e ValidationError) Error() string {
    return fmt.Sprintf("%s: %s", e.Field, e.Message)
}

func validateAge(age int) either.Either[ValidationError, int] {
    if age < 0 {
        return either.Left[int](ValidationError{
            Field:   "age",
            Message: "age cannot be negative",
        })
    }
    if age > 150 {
        return either.Left[int](ValidationError{
            Field:   "age",
            Message: "age cannot exceed 150",
        })
    }
    return either.Right[ValidationError](age)
}

// Chain validations
func validatePerson(name string, age int) either.Either[ValidationError, Person] {
    return either.Chain(
        validateAge(age),
        func(validAge int) either.Either[ValidationError, Person] {
            if name == "" {
                return either.Left[Person](ValidationError{
                    Field:   "name",
                    Message: "name cannot be empty",
                })
            }
            return either.Right[ValidationError](Person{Name: name, Age: validAge})
        },
    )
}
```

## Resources

- [IBM/fp-go Documentation](https://github.com/IBM/fp-go)
- [Functional Programming in Go](https://medium.com/@geisonfgfg/functional-go-bc116f4c96a4)
- [Monads in Go](https://medium.com/@geisonfgfg/monads-in-go-3b5b3b3b3b3b)
- Learning Functional Programming in Go book (in workspace)

## Validation Checklist

- [ ] fp-go library structure explored and understood
- [ ] Option monad implemented for nullable LangGraph values
- [ ] Either monad created for error handling in graph operations
- [ ] Functional composition utilities added for node processing
- [ ] Basic monadic patterns integrated into existing LangGraph code
- [ ] Performance comparison between functional and traditional approaches
