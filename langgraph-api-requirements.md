# LangGraph API Requirements Specification

## Overview

This document defines the comprehensive API requirements for the Go port of LangGraph, ensuring compatibility with the Python implementation while leveraging Go's type system and concurrency features.

## Core API Interfaces

### 1. Graph Construction API

#### StateGraph Interface

```go
type StateGraph[S any] interface {
    // Node management
    AddNode(id string, node Node[S]) error
    AddEdge(from, to string) error
    AddConditionalEdges(source string, path func(S) string, pathMap map[string]string) error
    
    // Graph compilation
    Compile(opts ...CompileOption) (CompiledGraph[S], error)
    
    // Graph introspection
    GetNodes() map[string]Node[S]
    GetEdges() []Edge
    GetEntryPoint() string
    GetFinishPoint() string
}

// Constructor function
func NewStateGraph[S any](schema StateSchema[S]) StateGraph[S]
```

#### Node Interface

```go
type Node[S any] interface {
    ID() string
    Execute(ctx context.Context, state S) (map[string]interface{}, error)
    GetInputChannels() []string
    GetOutputChannels() []string
}

// Function-based node constructor
func NewFunctionNode[S any](id string, fn func(context.Context, S) (map[string]interface{}, error)) Node[S]

// Runnable-based node constructor  
func NewRunnableNode[S any](id string, runnable Runnable) Node[S]
```

#### Edge Types

```go
type Edge struct {
    From string
    To   string
    Type EdgeType
}

type EdgeType int

const (
    NormalEdge EdgeType = iota
    ConditionalEdge
    StartEdge
    EndEdge
)

type ConditionalEdge struct {
    Edge
    Condition func(interface{}) string
    PathMap   map[string]string
}
```

### 2. Execution API

#### CompiledGraph Interface

```go
type CompiledGraph[S any] interface {
    // Synchronous execution
    Invoke(ctx context.Context, input interface{}, config ...Config) (interface{}, error)
    
    // Streaming execution
    Stream(ctx context.Context, input interface{}, config ...Config) (<-chan StreamChunk, error)
    
    // Asynchronous execution
    InvokeAsync(ctx context.Context, input interface{}, config ...Config) <-chan Result[interface{}]
    
    // Batch execution
    Batch(ctx context.Context, inputs []interface{}, config ...Config) ([]interface{}, error)
    
    // Graph introspection
    GetGraph() Graph
    GetInputSchema() Schema
    GetOutputSchema() Schema
    GetConfigSchema() Schema
}
```

#### Execution Configuration

```go
type Config struct {
    // Checkpointing
    Checkpointer    Checkpointer
    ThreadID        string
    CheckpointID    string
    CheckpointNS    string
    
    // Runtime configuration
    Runtime         Runtime
    MaxConcurrency  int
    Timeout         time.Duration
    
    // Debugging and observability
    Debug           bool
    StreamMode      StreamMode
    Callbacks       []Callback
    
    // Interrupts and control
    InterruptBefore []string
    InterruptAfter  []string
    
    // Retry and error handling
    RetryPolicy     RetryPolicy
    
    // Caching
    CachePolicy     CachePolicy
}
```

### 3. State Management API

#### StateSchema Interface

```go
type StateSchema[S any] interface {
    // Schema definition
    GetFields() map[string]FieldSpec
    GetReducers() map[string]Reducer
    Validate(state S) error
    Default() S
    
    // Channel creation
    CreateChannels() (map[string]Channel, error)
}

type FieldSpec struct {
    Type     reflect.Type
    Required bool
    Default  interface{}
    Reducer  Reducer
    Tags     map[string]string
}

type Reducer func(current, update interface{}) interface{}
```

#### Channel API

```go
type Channel interface {
    // Value operations
    Get() (interface{}, error)
    Update(values []interface{}) error
    
    // State management
    Checkpoint() (interface{}, error)
    FromCheckpoint(checkpoint interface{}) error
    
    // Metadata
    GetType() reflect.Type
    GetVersion() string
    IsEmpty() bool
}

// Channel types
type LastValueChannel[T any] interface {
    Channel
    GetValue() (T, error)
    SetValue(value T) error
}

type BinaryOpChannel[T any] interface {
    Channel
    GetAccumulator() (T, error)
    GetOperator() func(T, T) T
}

type TopicChannel[T any] interface {
    Channel
    Subscribe(subscriber string) (<-chan T, error)
    Publish(value T) error
    Unsubscribe(subscriber string) error
}
```

### 4. Checkpointing API

#### Checkpointer Interface

```go
type Checkpointer interface {
    // Checkpoint operations
    Put(ctx context.Context, config Config, checkpoint Checkpoint, metadata CheckpointMetadata) error
    Get(ctx context.Context, config Config) (CheckpointTuple, error)
    GetTuple(ctx context.Context, config Config) (CheckpointTuple, error)
    
    // Checkpoint listing and management
    List(ctx context.Context, config Config, filter CheckpointFilter, limit int, before string) ([]CheckpointTuple, error)
    
    // Async operations
    APut(ctx context.Context, config Config, checkpoint Checkpoint, metadata CheckpointMetadata) error
    AGet(ctx context.Context, config Config) (CheckpointTuple, error)
}

type Checkpoint struct {
    ChannelValues   map[string]interface{} `json:"channel_values"`
    ChannelVersions map[string]string      `json:"channel_versions"`
    VersionsSeen    map[string]map[string]string `json:"versions_seen"`
    PendingWrites   []PendingWrite         `json:"pending_writes"`
    ID              string                 `json:"id"`
    Timestamp       time.Time              `json:"ts"`
}

type CheckpointTuple struct {
    Config     Config
    Checkpoint Checkpoint
    Metadata   CheckpointMetadata
    ParentConfig *Config
}
```

### 5. Streaming API

#### Stream Types

```go
type StreamMode string

const (
    StreamModeValues   StreamMode = "values"
    StreamModeUpdates  StreamMode = "updates"
    StreamModeDebug    StreamMode = "debug"
    StreamModeMessages StreamMode = "messages"
)

type StreamChunk struct {
    Event     string                 `json:"event"`
    Data      interface{}           `json:"data"`
    Metadata  map[string]interface{} `json:"metadata,omitempty"`
    Timestamp time.Time             `json:"timestamp"`
}

// Streaming interface
type Streamer interface {
    Stream(ctx context.Context, input interface{}, config Config) (<-chan StreamChunk, error)
    StreamEvents(ctx context.Context, input interface{}, config Config, version string) (<-chan StreamEvent, error)
}
```

### 6. Error Handling API

#### Error Types

```go
type GraphError struct {
    Code    ErrorCode
    Message string
    NodeID  string
    Cause   error
}

type ErrorCode int

const (
    ErrorCodeInvalidInput ErrorCode = iota
    ErrorCodeNodeExecution
    ErrorCodeChannelUpdate
    ErrorCodeCheckpointSave
    ErrorCodeTimeout
    ErrorCodeCancellation
    ErrorCodeRecursionLimit
)

// Error handling interface
type ErrorHandler interface {
    HandleError(ctx context.Context, err error, nodeID string) (RecoveryAction, error)
    ShouldRetry(err error) bool
    GetRetryDelay(attempt int) time.Duration
}

type RecoveryAction int

const (
    RecoveryActionRetry RecoveryAction = iota
    RecoveryActionSkip
    RecoveryActionFallback
    RecoveryActionFail
)
```

### 7. Functional Programming Integration

#### Monadic Error Handling

```go
import "github.com/IBM/fp-go/either"

type GraphResult[T any] = either.Either[GraphError, T]

func (g *CompiledGraph[S]) InvokeEither(
    ctx context.Context, 
    input interface{}, 
    config ...Config,
) GraphResult[interface{}] {
    result, err := g.Invoke(ctx, input, config...)
    if err != nil {
        return either.Left[GraphError](GraphError{
            Code:    ErrorCodeNodeExecution,
            Message: err.Error(),
        })
    }
    return either.Right[interface{}](result)
}
```

#### State Monad Integration

```go
import "github.com/IBM/fp-go/state"

type GraphState[S any] struct {
    Channels map[string]Channel
    Step     int
    Config   Config
}

func UpdateState[S any](
    nodeID string, 
    update map[string]interface{},
) state.State[GraphState[S], unit.Unit] {
    return state.Modify[GraphState[S]](func(gs GraphState[S]) GraphState[S] {
        // Apply updates to channels
        for channel, value := range update {
            if ch, exists := gs.Channels[channel]; exists {
                ch.Update([]interface{}{value})
            }
        }
        return GraphState[S]{
            Channels: gs.Channels,
            Step:     gs.Step + 1,
            Config:   gs.Config,
        }
    })
}
```

### 8. Advanced Features API

#### Interrupts and Human-in-the-Loop

```go
type InterruptHandler interface {
    ShouldInterrupt(nodeID string, state interface{}) bool
    HandleInterrupt(ctx context.Context, nodeID string, state interface{}) (interface{}, error)
    Resume(ctx context.Context, checkpointID string, input interface{}) error
}

// Interrupt configuration
type InterruptConfig struct {
    Before []string
    After  []string
    Handler InterruptHandler
}
```

#### Subgraphs and Composition

```go
type SubgraphNode[S any] interface {
    Node[S]
    GetSubgraph() CompiledGraph[S]
    GetMapping() StateMapping
}

type StateMapping interface {
    MapInput(parentState interface{}) (interface{}, error)
    MapOutput(subgraphState interface{}) (map[string]interface{}, error)
}

func NewSubgraphNode[S any](
    id string, 
    subgraph CompiledGraph[S], 
    mapping StateMapping,
) SubgraphNode[S]
```

#### Conditional Routing

```go
type Router[S any] interface {
    Route(ctx context.Context, state S) (string, error)
    GetPaths() []string
}

func NewConditionalRouter[S any](
    condition func(S) string,
    paths map[string]string,
) Router[S]
```

## Implementation Requirements

### Type Safety

- Full generic type support for state types
- Compile-time type checking for node connections
- Schema validation for state transitions

### Concurrency

- Thread-safe channel operations
- Goroutine-based parallel execution
- Proper synchronization primitives

### Performance

- Memory-efficient state management
- Minimal allocations during execution
- Optimized serialization for checkpoints

### Compatibility

- Python LangGraph API compatibility where possible
- Migration path from Python implementations
- Interoperability with existing LangChain Go ecosystem

This API specification provides the foundation for implementing a complete, type-safe, and performant LangGraph port in Go that maintains compatibility with the Python version while leveraging Go's unique strengths.
