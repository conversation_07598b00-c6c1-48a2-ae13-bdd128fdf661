# Module 29: Unit Testing & Test Design

## Video Coverage

**12.1-12.4 Basic Unit Testing (13:54), Table Unit Testing (3:19), Mocking Web Server Response (6:59), Testing Internal Endpoints (7:22)**

## Learning Objectives

- Master Go testing fundamentals and patterns
- Understand table-driven tests and mocking
- Implement comprehensive test suite for LangGraph components

## Hands-on Tasks

1. **Implement testing examples from video transcripts**
   - Study Go's testing package and conventions
   - Practice basic unit test patterns
   - Understand test naming and organization
   - Implement examples from the video transcripts

2. **Create comprehensive unit tests for all LangGraph components**
   - Test core graph operations:

     ```go
     func TestGraph_AddNode(t *testing.T) {
         g := NewGraph("test-graph")
         node := NewNode("node1", func(ctx context.Context, input interface{}) (interface{}, error) {
             return "output", nil
         })
         
         err := g.AddNode(node)
         if err != nil {
             t.Fatalf("AddNode() error = %v, want nil", err)
         }
         
         if len(g.nodes) != 1 {
             t.<PERSON>("Expected 1 node, got %d", len(g.nodes))
         }
         
         retrievedNode, exists := g.nodes["node1"]
         if !exists {
             t.Error("Node was not added to graph")
         }
         
         if retrievedNode.ID() != "node1" {
             t.Errorf("Expected node ID 'node1', got '%s'", retrievedNode.ID())
         }
     }
     
     func TestGraph_AddNode_Duplicate(t *testing.T) {
         g := NewGraph("test-graph")
         node1 := NewNode("node1", nil)
         node2 := NewNode("node1", nil) // Same ID
         
         err1 := g.AddNode(node1)
         if err1 != nil {
             t.Fatalf("First AddNode() error = %v, want nil", err1)
         }
         
         err2 := g.AddNode(node2)
         if err2 == nil {
             t.Error("Expected error when adding duplicate node, got nil")
         }
     }
     
     func TestNode_Execute(t *testing.T) {
         expectedOutput := "test output"
         node := NewNode("test-node", func(ctx context.Context, input interface{}) (interface{}, error) {
             return expectedOutput, nil
         })
         
         output, err := node.Execute(context.Background(), "input")
         if err != nil {
             t.Fatalf("Execute() error = %v, want nil", err)
         }
         
         if output != expectedOutput {
             t.Errorf("Execute() output = %v, want %v", output, expectedOutput)
         }
     }
     ```

3. **Implement table-driven tests for node execution patterns**
   - Create comprehensive table-driven tests:

     ```go
     func TestNode_ExecuteTableDriven(t *testing.T) {
         tests := []struct {
             name           string
             nodeFunc       func(context.Context, interface{}) (interface{}, error)
             input          interface{}
             expectedOutput interface{}
             expectedError  bool
         }{
             {
                 name: "successful execution",
                 nodeFunc: func(ctx context.Context, input interface{}) (interface{}, error) {
                     return fmt.Sprintf("processed: %v", input), nil
                 },
                 input:          "test input",
                 expectedOutput: "processed: test input",
                 expectedError:  false,
             },
             {
                 name: "execution with error",
                 nodeFunc: func(ctx context.Context, input interface{}) (interface{}, error) {
                     return nil, errors.New("execution failed")
                 },
                 input:         "test input",
                 expectedError: true,
             },
             {
                 name: "nil input handling",
                 nodeFunc: func(ctx context.Context, input interface{}) (interface{}, error) {
                     if input == nil {
                         return "default output", nil
                     }
                     return input, nil
                 },
                 input:          nil,
                 expectedOutput: "default output",
                 expectedError:  false,
             },
             {
                 name: "context cancellation",
                 nodeFunc: func(ctx context.Context, input interface{}) (interface{}, error) {
                     select {
                     case <-ctx.Done():
                         return nil, ctx.Err()
                     case <-time.After(100 * time.Millisecond):
                         return "completed", nil
                     }
                 },
                 input:         "test input",
                 expectedError: true, // Context will be cancelled
             },
         }
         
         for _, tt := range tests {
             t.Run(tt.name, func(t *testing.T) {
                 node := NewNode("test-node", tt.nodeFunc)
                 
                 ctx := context.Background()
                 if tt.name == "context cancellation" {
                     var cancel context.CancelFunc
                     ctx, cancel = context.WithCancel(ctx)
                     cancel() // Cancel immediately
                 }
                 
                 output, err := node.Execute(ctx, tt.input)
                 
                 if tt.expectedError {
                     if err == nil {
                         t.Errorf("Expected error, got nil")
                     }
                 } else {
                     if err != nil {
                         t.Errorf("Unexpected error: %v", err)
                     }
                     
                     if output != tt.expectedOutput {
                         t.Errorf("Expected output %v, got %v", tt.expectedOutput, output)
                     }
                 }
             })
         }
     }
     
     func TestGraphExecution_TableDriven(t *testing.T) {
         tests := []struct {
             name          string
             setupGraph    func() *Graph
             expectedNodes int
             expectError   bool
         }{
             {
                 name: "single node execution",
                 setupGraph: func() *Graph {
                     g := NewGraph("single-node")
                     node := NewNode("node1", func(ctx context.Context, input interface{}) (interface{}, error) {
                         return "output1", nil
                     })
                     g.AddNode(node)
                     return g
                 },
                 expectedNodes: 1,
                 expectError:   false,
             },
             {
                 name: "multiple node execution",
                 setupGraph: func() *Graph {
                     g := NewGraph("multi-node")
                     for i := 1; i <= 3; i++ {
                         nodeID := fmt.Sprintf("node%d", i)
                         node := NewNode(nodeID, func(ctx context.Context, input interface{}) (interface{}, error) {
                             return fmt.Sprintf("output%d", i), nil
                         })
                         g.AddNode(node)
                     }
                     return g
                 },
                 expectedNodes: 3,
                 expectError:   false,
             },
             {
                 name: "empty graph",
                 setupGraph: func() *Graph {
                     return NewGraph("empty")
                 },
                 expectedNodes: 0,
                 expectError:   true,
             },
         }
         
         for _, tt := range tests {
             t.Run(tt.name, func(t *testing.T) {
                 graph := tt.setupGraph()
                 
                 result, err := graph.Execute(context.Background())
                 
                 if tt.expectError {
                     if err == nil {
                         t.Error("Expected error, got nil")
                     }
                 } else {
                     if err != nil {
                         t.Errorf("Unexpected error: %v", err)
                     }
                     
                     if len(result.Outputs) != tt.expectedNodes {
                         t.Errorf("Expected %d outputs, got %d", tt.expectedNodes, len(result.Outputs))
                     }
                 }
             })
         }
     }
     ```

4. **Add mocking for external dependencies**
   - Create mock implementations for testing:

     ```go
     // Mock state store for testing
     type MockStateStore struct {
         data   map[string]interface{}
         errors map[string]error
         calls  []string
         mu     sync.RWMutex
     }
     
     func NewMockStateStore() *MockStateStore {
         return &MockStateStore{
             data:   make(map[string]interface{}),
             errors: make(map[string]error),
             calls:  make([]string, 0),
         }
     }
     
     func (m *MockStateStore) Get(key string) (interface{}, error) {
         m.mu.Lock()
         defer m.mu.Unlock()
         
         m.calls = append(m.calls, fmt.Sprintf("Get(%s)", key))
         
         if err, exists := m.errors[key]; exists {
             return nil, err
         }
         
         value, exists := m.data[key]
         if !exists {
             return nil, fmt.Errorf("key %s not found", key)
         }
         
         return value, nil
     }
     
     func (m *MockStateStore) Set(key string, value interface{}) error {
         m.mu.Lock()
         defer m.mu.Unlock()
         
         m.calls = append(m.calls, fmt.Sprintf("Set(%s, %v)", key, value))
         
         if err, exists := m.errors[key]; exists {
             return err
         }
         
         m.data[key] = value
         return nil
     }
     
     func (m *MockStateStore) SetError(key string, err error) {
         m.mu.Lock()
         defer m.mu.Unlock()
         m.errors[key] = err
     }
     
     func (m *MockStateStore) GetCalls() []string {
         m.mu.RLock()
         defer m.mu.RUnlock()
         
         calls := make([]string, len(m.calls))
         copy(calls, m.calls)
         return calls
     }
     
     func TestNodeWithStateStore(t *testing.T) {
         mockStore := NewMockStateStore()
         
         node := NewStatefulNode("stateful-node", mockStore, func(ctx context.Context, store StateStore, input interface{}) (interface{}, error) {
             // Store some state
             if err := store.Set("last_input", input); err != nil {
                 return nil, err
             }
             
             // Retrieve previous state
             prev, err := store.Get("previous_output")
             if err != nil {
                 prev = "no previous output"
             }
             
             return fmt.Sprintf("current: %v, previous: %v", input, prev), nil
         })
         
         // First execution
         output1, err := node.Execute(context.Background(), "input1")
         if err != nil {
             t.Fatalf("First execution failed: %v", err)
         }
         
         expected1 := "current: input1, previous: no previous output"
         if output1 != expected1 {
             t.Errorf("Expected %s, got %s", expected1, output1)
         }
         
         // Set up state for second execution
         mockStore.Set("previous_output", "output1")
         
         // Second execution
         output2, err := node.Execute(context.Background(), "input2")
         if err != nil {
             t.Fatalf("Second execution failed: %v", err)
         }
         
         expected2 := "current: input2, previous: output1"
         if output2 != expected2 {
             t.Errorf("Expected %s, got %s", expected2, output2)
         }
         
         // Verify mock calls
         calls := mockStore.GetCalls()
         expectedCalls := []string{
             "Set(last_input, input1)",
             "Get(previous_output)",
             "Set(last_input, input2)",
             "Get(previous_output)",
         }
         
         if len(calls) != len(expectedCalls) {
             t.Errorf("Expected %d calls, got %d", len(expectedCalls), len(calls))
         }
     }
     ```

5. **Create test utilities and helpers for LangGraph testing**
   - Build comprehensive test utilities:

     ```go
     // Test utilities package
     package testutil
     
     import (
         "context"
         "fmt"
         "testing"
         "time"
     )
     
     // TestGraph creates a graph with predefined nodes for testing
     func CreateTestGraph(t *testing.T, nodeCount int) *Graph {
         t.Helper()
         
         graph := NewGraph(fmt.Sprintf("test-graph-%d", nodeCount))
         
         for i := 1; i <= nodeCount; i++ {
             nodeID := fmt.Sprintf("node%d", i)
             node := NewNode(nodeID, func(ctx context.Context, input interface{}) (interface{}, error) {
                 return fmt.Sprintf("output%d", i), nil
             })
             
             if err := graph.AddNode(node); err != nil {
                 t.Fatalf("Failed to add node %s: %v", nodeID, err)
             }
         }
         
         return graph
     }
     
     // AssertGraphExecution verifies graph execution results
     func AssertGraphExecution(t *testing.T, graph *Graph, expectedOutputs int) *ExecutionResult {
         t.Helper()
         
         ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
         defer cancel()
         
         result, err := graph.Execute(ctx)
         if err != nil {
             t.Fatalf("Graph execution failed: %v", err)
         }
         
         if len(result.Outputs) != expectedOutputs {
             t.Errorf("Expected %d outputs, got %d", expectedOutputs, len(result.Outputs))
         }
         
         if result.EndTime.Before(result.StartTime) {
             t.Error("End time should be after start time")
         }
         
         return result
     }
     
     // AssertNodeExists verifies a node exists in the graph
     func AssertNodeExists(t *testing.T, graph *Graph, nodeID string) {
         t.Helper()
         
         if _, exists := graph.nodes[nodeID]; !exists {
             t.Errorf("Node %s does not exist in graph", nodeID)
         }
     }
     
     // AssertError verifies that an error occurred and optionally checks the message
     func AssertError(t *testing.T, err error, expectedMessage string) {
         t.Helper()
         
         if err == nil {
             t.Fatal("Expected error, got nil")
         }
         
         if expectedMessage != "" && err.Error() != expectedMessage {
             t.Errorf("Expected error message '%s', got '%s'", expectedMessage, err.Error())
         }
     }
     
     // AssertNoError verifies that no error occurred
     func AssertNoError(t *testing.T, err error) {
         t.Helper()
         
         if err != nil {
             t.Fatalf("Expected no error, got: %v", err)
         }
     }
     
     // BenchmarkGraphExecution provides a standard benchmark for graph execution
     func BenchmarkGraphExecution(b *testing.B, nodeCount int) {
         graph := NewGraph(fmt.Sprintf("benchmark-graph-%d", nodeCount))
         
         for i := 1; i <= nodeCount; i++ {
             nodeID := fmt.Sprintf("node%d", i)
             node := NewNode(nodeID, func(ctx context.Context, input interface{}) (interface{}, error) {
                 // Simulate some work
                 time.Sleep(time.Microsecond)
                 return fmt.Sprintf("output%d", i), nil
             })
             graph.AddNode(node)
         }
         
         b.ResetTimer()
         b.ReportAllocs()
         
         for i := 0; i < b.N; i++ {
             _, err := graph.Execute(context.Background())
             if err != nil {
                 b.Fatalf("Graph execution failed: %v", err)
             }
         }
     }
     ```

## Key Concepts

- **Unit testing**: Testing individual components in isolation
- **Table-driven tests**: Using data tables to test multiple scenarios
- **Mocking**: Creating fake implementations for testing
- **Test design**: Structuring tests for maintainability and clarity

## Code Examples

### Basic Unit Test

```go
func TestAdd(t *testing.T) {
    result := Add(2, 3)
    expected := 5
    
    if result != expected {
        t.Errorf("Add(2, 3) = %d; want %d", result, expected)
    }
}
```

### Table-Driven Test

```go
func TestAdd(t *testing.T) {
    tests := []struct {
        name     string
        a, b     int
        expected int
    }{
        {"positive numbers", 2, 3, 5},
        {"negative numbers", -1, -2, -3},
        {"zero", 0, 5, 5},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result := Add(tt.a, tt.b)
            if result != tt.expected {
                t.Errorf("Add(%d, %d) = %d; want %d", tt.a, tt.b, result, tt.expected)
            }
        })
    }
}
```

### Mock Implementation

```go
type MockHTTPClient struct {
    responses map[string]string
    errors    map[string]error
}

func (m *MockHTTPClient) Get(url string) (string, error) {
    if err, exists := m.errors[url]; exists {
        return "", err
    }
    
    if response, exists := m.responses[url]; exists {
        return response, nil
    }
    
    return "", fmt.Errorf("no mock response for %s", url)
}
```

## Resources

- Ultimate Go Programming 12.1-12.4 transcripts
- [Go Testing Package](https://golang.org/pkg/testing/)
- [Table-Driven Tests](https://github.com/golang/go/wiki/TableDrivenTests)
- [Go by Example - Testing](https://gobyexample.com/testing)

## Validation Checklist

- [ ] Testing examples from transcripts implemented and understood
- [ ] Comprehensive unit tests created for all LangGraph components
- [ ] Table-driven tests implemented for node execution patterns
- [ ] Mocking added for external dependencies
- [ ] Test utilities and helpers created for LangGraph testing
- [ ] All tests pass and provide good coverage of core functionality
