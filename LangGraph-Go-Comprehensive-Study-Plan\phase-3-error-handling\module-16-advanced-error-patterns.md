# Module 16: Advanced Error Patterns

## Video Coverage

**6.4-6.6 Behavior as Context (9:50), Find the Bug (8:52), Wrapping Errors (14:30)**

## Learning Objectives

- Implement behavior-based error handling
- Master error debugging and wrapping techniques
- Create sophisticated error handling for LangGraph

## Hands-on Tasks

1. **Implement advanced error examples from video transcripts**
   - Study behavior-based error patterns from the video
   - Practice error debugging techniques
   - Understand error wrapping chains and context
   - Implement examples showing advanced error handling

2. **Add behavior-based error interfaces for LangGraph**
   - Create interfaces that provide error behavior:

     ```go
     // Temporary error interface
     type Temporary interface {
         Temporary() bool
     }
     
     // Timeout error interface
     type Timeout interface {
         Timeout() bool
     }
     
     // Retryable error interface
     type Retryable interface {
         Retryable() bool
         RetryAfter() time.Duration
     }
     
     // Recoverable error interface
     type Recoverable interface {
         Recoverable() bool
         RecoveryAction() string
     }
     
     // Implementation in LangGraph errors
     type ExecutionError struct {
         NodeID      string
         Err         error
         IsTemp      bool
         IsTimeout   bool
         CanRetry    bool
         RetryDelay  time.Duration
         CanRecover  bool
         Recovery    string
     }
     
     func (e *ExecutionError) Error() string {
         return fmt.Sprintf("execution failed for node %s: %v", e.NodeID, e.Err)
     }
     
     func (e *ExecutionError) Temporary() bool {
         return e.IsTemp
     }
     
     func (e *ExecutionError) Timeout() bool {
         return e.IsTimeout
     }
     
     func (e *ExecutionError) Retryable() bool {
         return e.CanRetry
     }
     
     func (e *ExecutionError) RetryAfter() time.Duration {
         return e.RetryDelay
     }
     
     func (e *ExecutionError) Recoverable() bool {
         return e.CanRecover
     }
     
     func (e *ExecutionError) RecoveryAction() string {
         return e.Recovery
     }
     ```

3. **Implement error wrapping chains for execution context**
   - Create sophisticated error wrapping:

     ```go
     type ErrorChain struct {
         errors []error
         context map[string]interface{}
     }
     
     func NewErrorChain() *ErrorChain {
         return &ErrorChain{
             errors:  make([]error, 0),
             context: make(map[string]interface{}),
         }
     }
     
     func (ec *ErrorChain) Add(err error) *ErrorChain {
         if err != nil {
             ec.errors = append(ec.errors, err)
         }
         return ec
     }
     
     func (ec *ErrorChain) AddContext(key string, value interface{}) *ErrorChain {
         ec.context[key] = value
         return ec
     }
     
     func (ec *ErrorChain) Error() string {
         if len(ec.errors) == 0 {
             return "no errors"
         }
         
         var builder strings.Builder
         builder.WriteString("error chain:")
         
         for i, err := range ec.errors {
             builder.WriteString(fmt.Sprintf("\n  %d: %v", i+1, err))
         }
         
         if len(ec.context) > 0 {
             builder.WriteString("\ncontext:")
             for k, v := range ec.context {
                 builder.WriteString(fmt.Sprintf("\n  %s: %v", k, v))
             }
         }
         
         return builder.String()
     }
     
     func (ec *ErrorChain) Unwrap() error {
         if len(ec.errors) == 0 {
             return nil
         }
         return ec.errors[len(ec.errors)-1]
     }
     
     func (ec *ErrorChain) HasErrors() bool {
         return len(ec.errors) > 0
     }
     
     func (ec *ErrorChain) GetErrors() []error {
         return ec.errors
     }
     ```

4. **Create debugging utilities for error analysis**
   - Implement error analysis and debugging tools:

     ```go
     type ErrorAnalyzer struct {
         logger Logger
     }
     
     func NewErrorAnalyzer(logger Logger) *ErrorAnalyzer {
         return &ErrorAnalyzer{logger: logger}
     }
     
     func (ea *ErrorAnalyzer) AnalyzeError(err error) *ErrorReport {
         if err == nil {
             return &ErrorReport{Type: "no_error"}
         }
         
         report := &ErrorReport{
             Type:      reflect.TypeOf(err).String(),
             Message:   err.Error(),
             Timestamp: time.Now(),
             Stack:     ea.getStackTrace(),
         }
         
         // Analyze error behavior
         if temp, ok := err.(Temporary); ok {
             report.IsTemporary = temp.Temporary()
         }
         
         if timeout, ok := err.(Timeout); ok {
             report.IsTimeout = timeout.Timeout()
         }
         
         if retryable, ok := err.(Retryable); ok {
             report.IsRetryable = retryable.Retryable()
             report.RetryAfter = retryable.RetryAfter()
         }
         
         // Unwrap error chain
         report.Chain = ea.unwrapChain(err)
         
         return report
     }
     
     func (ea *ErrorAnalyzer) getStackTrace() []string {
         // Implementation to capture stack trace
         return []string{"stack trace implementation"}
     }
     
     func (ea *ErrorAnalyzer) unwrapChain(err error) []string {
         var chain []string
         for err != nil {
             chain = append(chain, err.Error())
             err = errors.Unwrap(err)
         }
         return chain
     }
     
     type ErrorReport struct {
         Type        string        `json:"type"`
         Message     string        `json:"message"`
         Timestamp   time.Time     `json:"timestamp"`
         Stack       []string      `json:"stack"`
         Chain       []string      `json:"chain"`
         IsTemporary bool          `json:"is_temporary"`
         IsTimeout   bool          `json:"is_timeout"`
         IsRetryable bool          `json:"is_retryable"`
         RetryAfter  time.Duration `json:"retry_after"`
     }
     ```

5. **Add comprehensive error testing and validation**
   - Create error testing utilities:

     ```go
     func TestErrorBehavior(t *testing.T) {
         tests := []struct {
             name     string
             err      error
             wantTemp bool
             wantRetry bool
         }{
             {
                 name: "temporary network error",
                 err: &ExecutionError{
                     NodeID:     "node1",
                     Err:        errors.New("network timeout"),
                     IsTemp:     true,
                     CanRetry:   true,
                     RetryDelay: 5 * time.Second,
                 },
                 wantTemp:  true,
                 wantRetry: true,
             },
             {
                 name: "permanent validation error",
                 err: &ExecutionError{
                     NodeID:   "node2",
                     Err:      errors.New("invalid input"),
                     IsTemp:   false,
                     CanRetry: false,
                 },
                 wantTemp:  false,
                 wantRetry: false,
             },
         }
         
         for _, tt := range tests {
             t.Run(tt.name, func(t *testing.T) {
                 if temp, ok := tt.err.(Temporary); ok {
                     if got := temp.Temporary(); got != tt.wantTemp {
                         t.Errorf("Temporary() = %v, want %v", got, tt.wantTemp)
                     }
                 }
                 
                 if retry, ok := tt.err.(Retryable); ok {
                     if got := retry.Retryable(); got != tt.wantRetry {
                         t.Errorf("Retryable() = %v, want %v", got, tt.wantRetry)
                     }
                 }
             })
         }
     }
     
     func TestErrorWrapping(t *testing.T) {
         originalErr := errors.New("original error")
         wrappedErr := fmt.Errorf("wrapped: %w", originalErr)
         
         // Test unwrapping
         if !errors.Is(wrappedErr, originalErr) {
             t.Error("wrapped error should contain original error")
         }
         
         // Test error chain
         chain := NewErrorChain()
         chain.Add(originalErr).Add(wrappedErr).AddContext("node_id", "test_node")
         
         if !chain.HasErrors() {
             t.Error("error chain should have errors")
         }
         
         if len(chain.GetErrors()) != 2 {
             t.Errorf("expected 2 errors, got %d", len(chain.GetErrors()))
         }
     }
     ```

## Key Concepts

- **Error behavior**: Interfaces that describe error characteristics
- **Debugging**: Tools and techniques for error analysis
- **Error wrapping**: Creating chains of contextual errors
- **Context chains**: Preserving execution context through errors

## Code Examples

### Behavior-Based Error Handling

```go
func handleError(err error) {
    if err == nil {
        return
    }
    
    // Check if error is temporary
    if temp, ok := err.(interface{ Temporary() bool }); ok && temp.Temporary() {
        log.Println("Temporary error, will retry")
        return
    }
    
    // Check if error is timeout
    if timeout, ok := err.(interface{ Timeout() bool }); ok && timeout.Timeout() {
        log.Println("Timeout error, increasing timeout")
        return
    }
    
    // Check if error is retryable
    if retry, ok := err.(interface{ 
        Retryable() bool
        RetryAfter() time.Duration 
    }); ok && retry.Retryable() {
        delay := retry.RetryAfter()
        log.Printf("Retryable error, waiting %v", delay)
        time.Sleep(delay)
        return
    }
    
    log.Printf("Permanent error: %v", err)
}
```

### Error Debugging Utilities

```go
func debugError(err error) {
    if err == nil {
        return
    }
    
    fmt.Printf("Error: %v\n", err)
    fmt.Printf("Type: %T\n", err)
    
    // Print error chain
    fmt.Println("Error chain:")
    for i, e := range unwrapAll(err) {
        fmt.Printf("  %d: %v\n", i, e)
    }
    
    // Check error behaviors
    if temp, ok := err.(Temporary); ok {
        fmt.Printf("Temporary: %v\n", temp.Temporary())
    }
    
    if timeout, ok := err.(Timeout); ok {
        fmt.Printf("Timeout: %v\n", timeout.Timeout())
    }
}

func unwrapAll(err error) []error {
    var errors []error
    for err != nil {
        errors = append(errors, err)
        err = errors.Unwrap(err)
    }
    return errors
}
```

## Resources

- Ultimate Go Programming 6.4-6.6 transcripts
- [Error Wrapping in Go 1.13](https://blog.golang.org/go1.13-errors)
- [Error Handling Best Practices](https://dave.cheney.net/2016/04/27/dont-just-check-errors-handle-them-gracefully)
- [Go Error Handling Patterns](https://github.com/golang/go/wiki/ErrorHandling)

## Validation Checklist

- [ ] Advanced error examples from transcripts implemented
- [ ] Behavior-based error interfaces added to LangGraph
- [ ] Error wrapping chains implemented for execution context
- [ ] Debugging utilities created for error analysis
- [ ] Comprehensive error testing and validation added
- [ ] Error behavior patterns documented and tested
