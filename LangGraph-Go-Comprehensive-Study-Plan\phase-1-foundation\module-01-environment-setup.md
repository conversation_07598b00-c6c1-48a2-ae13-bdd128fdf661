# Module 1: Environment Setup & Go Basics

## Video Coverage

**2.1 Variables (16:26)**

## Learning Objectives

- Set up Go development environment and toolchain
- Understand Go's type system and memory model
- Master variable declarations and zero values
- Create project structure for LangGraph port

## Hands-on Tasks

1. **Install Go and set up development environment**
   - Download and install Go from official website
   - Configure GOPATH and GOROOT environment variables
   - Set up IDE/editor with Go support (VS Code with Go extension recommended)
   - Verify installation with `go version` command

2. **Create `langgraph-go` project with proper module structure**
   - Initialize new Go module: `go mod init github.com/yourusername/langgraph-go`
   - Create basic directory structure:

     ```bash
     langgraph-go/
     ├── cmd/
     ├── pkg/
     ├── internal/
     ├── examples/
     ├── docs/
     └── tests/
     ```

   - Add initial go.mod and go.sum files
   - Create .gitignore file for Go projects

3. **Implement basic type demonstrations from video transcript**
   - Create examples of Go's built-in types (int, float64, string, bool)
   - Demonstrate zero values for each type
   - Show variable declaration patterns (var vs :=)
   - Implement examples from the Variables video transcript

4. **Create foundation packages: `core`, `types`, `errors`**
   - Create `pkg/core/` package for core LangGraph functionality
   - Create `pkg/types/` package for type definitions
   - Create `pkg/errors/` package for error handling
   - Add basic package documentation and structure

5. **Implement zero-value initialization patterns for LangGraph state types**
   - Define basic state types with proper zero values
   - Create initialization functions that leverage Go's zero value guarantees
   - Implement state validation using zero value checks
   - Add examples demonstrating zero value behavior

## Key Concepts

- **Type safety**: Go's strong typing system and compile-time type checking
- **Zero values**: Go's guarantee that all memory is initialized to zero values
- **Memory layout**: Understanding how Go organizes data in memory
- **Go toolchain**: go build, go run, go test, go mod commands

## Prerequisites for Next Module

- Go development environment fully configured
- Basic project structure created
- Understanding of Go's type system fundamentals
- Familiarity with zero values and variable declarations

## Resources

- [Go Installation Guide](https://golang.org/doc/install)
- [How to Write Go Code](https://golang.org/doc/code.html)
- Ultimate Go Programming 2.1 Variables transcript
- [Go by Example - Variables](https://gobyexample.com/variables)

## Validation Checklist

- [ ] Go installed and `go version` works
- [ ] Project structure created with proper module initialization
- [ ] Basic type examples implemented and running
- [ ] Foundation packages created with documentation
- [ ] Zero value patterns implemented and tested
