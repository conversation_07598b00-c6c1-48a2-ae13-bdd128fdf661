# Module 8: Maps & Hash-Based Storage

## Video Coverage

**3.4 Maps (8:03)**

## Learning Objectives

- Understand map implementation and hash tables
- Master key-value storage patterns
- Implement efficient lookup structures for LangGraph

## Hands-on Tasks

1. **Implement map examples from video transcript**
   - Create maps using make() and literal syntax
   - Demonstrate map operations: insert, lookup, delete
   - Show zero value behavior and nil map handling
   - Practice map iteration patterns

2. **Create hash-based lookup tables for nodes and channels**
   - Implement efficient node registry:

     ```go
     type NodeRegistry struct {
         nodes    map[string]*Node
         byType   map[NodeType][]string
         metadata map[string]map[string]interface{}
         mu       sync.RWMutex
     }
     
     func NewNodeRegistry() *NodeRegistry {
         return &NodeRegistry{
             nodes:    make(map[string]*Node),
             byType:   make(map[NodeType][]string),
             metadata: make(map[string]map[string]interface{}),
         }
     }
     
     func (nr *NodeRegistry) Register(node *Node) {
         nr.mu.Lock()
         defer nr.mu.Unlock()
         
         nr.nodes[node.ID] = node
         nr.byType[node.Type] = append(nr.byType[node.Type], node.ID)
         nr.metadata[node.ID] = make(map[string]interface{})
     }
     
     func (nr *NodeRegistry) Lookup(id string) (*Node, bool) {
         nr.mu.RLock()
         defer nr.mu.RUnlock()
         
         node, exists := nr.nodes[id]
         return node, exists
     }
     ```

3. **Implement efficient state storage using maps**
   - Create state management system with maps
   - Implement nested map structures for complex state
   - Add state versioning and history tracking
   - Create efficient state diff algorithms

4. **Add map-based caching for LangGraph execution**
   - Implement LRU cache using maps:

     ```go
     type LRUCache struct {
         capacity int
         cache    map[string]*CacheNode
         head     *CacheNode
         tail     *CacheNode
     }
     
     type CacheNode struct {
         key   string
         value interface{}
         prev  *CacheNode
         next  *CacheNode
     }
     
     func NewLRUCache(capacity int) *LRUCache {
         head := &CacheNode{}
         tail := &CacheNode{}
         head.next = tail
         tail.prev = head
         
         return &LRUCache{
             capacity: capacity,
             cache:    make(map[string]*CacheNode),
             head:     head,
             tail:     tail,
         }
     }
     ```

5. **Optimize map operations for concurrent access patterns**
   - Implement concurrent-safe map operations
   - Use sync.Map for high-concurrency scenarios
   - Create sharded maps for better performance
   - Add benchmarks comparing different map strategies

## Key Concepts

- **Maps**: Hash table-based key-value data structures
- **Hash tables**: Efficient data structure for lookups
- **Key-value storage**: Associative data storage patterns
- **Lookup efficiency**: O(1) average case performance

## Code Examples

### Map Basics

```go
// Creating maps
var m1 map[string]int               // nil map
m2 := make(map[string]int)          // empty map
m3 := map[string]int{               // map literal
    "apple":  5,
    "banana": 3,
    "orange": 8,
}

// Map operations
m2["key"] = 42                      // insert/update
value, exists := m2["key"]          // lookup with existence check
delete(m2, "key")                   // delete

// Iteration
for key, value := range m3 {
    fmt.Printf("%s: %d\n", key, value)
}
```

### Safe Map Operations

```go
func safeMapLookup(m map[string]int, key string) (int, error) {
    if m == nil {
        return 0, fmt.Errorf("map is nil")
    }
    
    value, exists := m[key]
    if !exists {
        return 0, fmt.Errorf("key %s not found", key)
    }
    
    return value, nil
}
```

### Concurrent Map with Sharding

```go
type ShardedMap struct {
    shards []map[string]interface{}
    locks  []sync.RWMutex
    count  int
}

func NewShardedMap(shardCount int) *ShardedMap {
    sm := &ShardedMap{
        shards: make([]map[string]interface{}, shardCount),
        locks:  make([]sync.RWMutex, shardCount),
        count:  shardCount,
    }
    
    for i := range sm.shards {
        sm.shards[i] = make(map[string]interface{})
    }
    
    return sm
}

func (sm *ShardedMap) getShard(key string) int {
    hash := fnv.New32a()
    hash.Write([]byte(key))
    return int(hash.Sum32()) % sm.count
}

func (sm *ShardedMap) Set(key string, value interface{}) {
    shard := sm.getShard(key)
    sm.locks[shard].Lock()
    defer sm.locks[shard].Unlock()
    
    sm.shards[shard][key] = value
}

func (sm *ShardedMap) Get(key string) (interface{}, bool) {
    shard := sm.getShard(key)
    sm.locks[shard].RLock()
    defer sm.locks[shard].RUnlock()
    
    value, exists := sm.shards[shard][key]
    return value, exists
}
```

### State Management System

```go
type StateManager struct {
    current  map[string]interface{}
    history  []map[string]interface{}
    maxHist  int
    mu       sync.RWMutex
}

func NewStateManager(maxHistory int) *StateManager {
    return &StateManager{
        current: make(map[string]interface{}),
        history: make([]map[string]interface{}, 0, maxHistory),
        maxHist: maxHistory,
    }
}

func (sm *StateManager) Set(key string, value interface{}) {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    
    // Save current state to history
    if len(sm.current) > 0 {
        snapshot := make(map[string]interface{})
        for k, v := range sm.current {
            snapshot[k] = v
        }
        
        sm.history = append(sm.history, snapshot)
        if len(sm.history) > sm.maxHist {
            sm.history = sm.history[1:]
        }
    }
    
    sm.current[key] = value
}

func (sm *StateManager) Get(key string) (interface{}, bool) {
    sm.mu.RLock()
    defer sm.mu.RUnlock()
    
    value, exists := sm.current[key]
    return value, exists
}

func (sm *StateManager) Rollback() bool {
    sm.mu.Lock()
    defer sm.mu.Unlock()
    
    if len(sm.history) == 0 {
        return false
    }
    
    // Restore previous state
    lastIndex := len(sm.history) - 1
    sm.current = sm.history[lastIndex]
    sm.history = sm.history[:lastIndex]
    
    return true
}
```

## Resources

- Ultimate Go Programming 3.4 Maps transcript
- [Go Maps in Action](https://blog.golang.org/maps)
- [Go by Example - Maps](https://gobyexample.com/maps)
- [Effective Go - Maps](https://golang.org/doc/effective_go#maps)

## Validation Checklist

- [ ] Map examples from transcript implemented
- [ ] Node and channel lookup tables created
- [ ] State storage system implemented with maps
- [ ] Map-based caching system built
- [ ] Concurrent access patterns optimized and tested
